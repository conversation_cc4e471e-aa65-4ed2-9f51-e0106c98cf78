'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { MessageCircle, Users, Clock, CheckCircle2, Sparkles } from 'lucide-react';
import { ChatRoom as ChatRoomType } from '@/lib/chat';
import { getUserChatRooms } from '@/lib/chat';
import { useAuth } from '@/contexts/AuthContext';

interface ChatListProps {
  onSelectRoom: (room: ChatRoomType) => void;
}

export function ChatList({ onSelectRoom }: ChatListProps) {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<ChatRoomType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadChatRooms();
    }
  }, [user]);

  const loadChatRooms = async () => {
    setLoading(true);
    try {
      const { data, error } = await getUserChatRooms();
      if (error) {
        console.error('Error loading chat rooms:', error);
      } else {
        setRooms(data || []);
      }
    } catch (error) {
      console.error('Error loading chat rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string | null | undefined) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatLastMessageTime = (timestamp: string | null) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('sr-RS', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('sr-RS', {
        day: 'numeric',
        month: 'short'
      });
    }
  };

  const getOtherParticipant = (room: ChatRoomType) => {
    if (!user) return null;
    const userType = user.user_metadata?.user_type || 'influencer';
    
    if (userType === 'business') {
      return room.influencer_profile;
    } else {
      return room.business_profile;
    }
  };

  const getRoomTypeLabel = (roomType: string) => {
    switch (roomType) {
      case 'campaign_application':
        return 'Kampanja';
      case 'direct_offer':
        return 'Direktna ponuda';
      default:
        return 'Chat';
    }
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
          <CardTitle className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageCircle className="h-5 w-5 text-blue-600" />
            </div>
            Poruke
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-center gap-3 p-3 rounded-lg border">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                  <Skeleton className="h-3 w-24" />
                  <Skeleton className="h-3 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (rooms.length === 0) {
    return (
      <Card className="h-full">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
          <CardTitle className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageCircle className="h-5 w-5 text-blue-600" />
            </div>
            Poruke
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center text-muted-foreground py-12">
            <div className="relative mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Users className="h-10 w-10 text-blue-500" />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                <Sparkles className="h-3 w-3 text-yellow-600" />
              </div>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Nemate aktivne razgovore</h3>
            <p className="text-sm mb-4">Razgovori će se pojaviti kada prihvatite ponude ili aplikacije.</p>
            <div className="flex items-center justify-center gap-4 text-xs">
              <div className="flex items-center gap-1 bg-blue-50 px-3 py-1 rounded-full">
                <CheckCircle2 className="h-3 w-3 text-blue-600" />
                <span className="text-blue-700">Sigurno</span>
              </div>
              <div className="flex items-center gap-1 bg-green-50 px-3 py-1 rounded-full">
                <Clock className="h-3 w-3 text-green-600" />
                <span className="text-green-700">Trenutno</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageCircle className="h-5 w-5 text-blue-600" />
            </div>
            Poruke
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {rooms.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 flex-1 overflow-y-auto">
        <div className="space-y-1 p-2">
          {rooms.map((room) => {
            const otherParticipant = getOtherParticipant(room);
            const hasUnread = room.unread_count && room.unread_count > 0;

            return (
              <Button
                key={room.id}
                variant="ghost"
                className={`w-full justify-start p-4 h-auto transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md rounded-lg ${
                  hasUnread ? 'bg-blue-50/50 border border-blue-200' : ''
                }`}
                onClick={() => onSelectRoom(room)}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="relative">
                    <Avatar className="h-12 w-12 ring-2 ring-white shadow-sm">
                      <AvatarImage src={otherParticipant?.avatar_url || ''} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                        {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}
                      </AvatarFallback>
                    </Avatar>
                    {hasUnread && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold text-white">{room.unread_count}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex-1 text-left">
                    <div className="flex items-center justify-between">
                      <h4 className={`font-medium ${hasUnread ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>
                        {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}
                      </h4>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          {formatLastMessageTime(room.last_message_at)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-muted-foreground">
                        @{otherParticipant?.username || 'nepoznato'}
                      </p>
                      <Badge
                        variant="outline"
                        className={`text-xs ${
                          room.room_type === 'campaign_application'
                            ? 'border-green-200 bg-green-50 text-green-700'
                            : 'border-blue-200 bg-blue-50 text-blue-700'
                        }`}
                      >
                        {getRoomTypeLabel(room.room_type)}
                      </Badge>
                    </div>

                    <p className={`text-sm mt-1 truncate ${hasUnread ? 'font-medium text-gray-800' : 'text-muted-foreground'}`}>
                      {room.room_title}
                    </p>
                  </div>
                </div>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
