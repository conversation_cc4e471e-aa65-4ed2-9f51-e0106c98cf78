'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Send, ArrowLeft, Clock, CheckCircle2, User, MessageCircle } from 'lucide-react';
import { ChatMessage, ChatRoom as ChatRoomType } from '@/lib/chat';
import { getChatMessages, sendChatMessage, markMessagesAsRead } from '@/lib/chat';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { ChatContextBar } from './ChatContextBar';

interface ChatRoomProps {
  room: ChatRoomType;
  onBack: () => void;
}

export function ChatRoom({ room, onBack }: ChatRoomProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    loadMessages();
    
    // Subscribe to new messages
    const channel = supabase
      .channel(`chat_room_${room.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${room.id}`,
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          setMessages(prev => [...prev, newMessage]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [room.id]);

  const loadMessages = async () => {
    setLoading(true);
    try {
      const { data, error } = await getChatMessages(room.id);
      if (error) {
        console.error('Error loading messages:', error);
      } else {
        setMessages(data || []);
        // Mark unread messages as read (messages not sent by current user)
        if (user && data) {
          const unreadMessageIds = data
            .filter(msg => msg.sender_id !== user.id && !msg.read_at)
            .map(msg => msg.id);

          if (unreadMessageIds.length > 0) {
            await markMessagesAsRead(room.id, unreadMessageIds);
          }
        }
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !user || sending) return;

    setSending(true);
    try {
      const userType = user.user_metadata?.user_type || 'influencer';
      const { data, error } = await sendChatMessage(
        room.id,
        newMessage.trim()
      );

      if (error) {
        console.error('Error sending message:', error);
      } else {
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const getInitials = (name: string | null | undefined) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('sr-RS', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('sr-RS', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const isMyMessage = (message: ChatMessage) => {
    return user && message.sender_id === user.id;
  };

  const getOtherParticipant = () => {
    if (!user) return null;
    const userType = user.user_metadata?.user_type || 'influencer';
    
    if (userType === 'business') {
      return room.influencer_profile;
    } else {
      return room.business_profile;
    }
  };

  const otherParticipant = getOtherParticipant();

  return (
    <div className="h-full flex flex-col">
      {/* Context Bar */}
      <ChatContextBar
        campaignApplicationId={room.campaign_application_id}
        offerId={room.offer_id}
      />

      <Card className="flex-1 flex flex-col shadow-lg">
        {/* Enhanced Header */}
        <CardHeader className="flex-shrink-0 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="p-2 hover:bg-white/80 rounded-full"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <div className="relative">
              <Avatar className="h-12 w-12 ring-2 ring-white shadow-md">
                <AvatarImage src={otherParticipant?.avatar_url || ''} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                  {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            </div>

            <div className="flex-1">
              <CardTitle className="text-lg text-gray-900">
                {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}
              </CardTitle>
              <div className="flex items-center gap-2">
                <p className="text-sm text-muted-foreground">
                  @{otherParticipant?.username || 'nepoznato'}
                </p>
                <div className="flex items-center gap-1 text-xs text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Online</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className={`${
                  room.room_type === 'campaign_application'
                    ? 'bg-green-100 text-green-800 border-green-200'
                    : 'bg-blue-100 text-blue-800 border-blue-200'
                }`}
              >
                <MessageCircle className="h-3 w-3 mr-1" />
                {room.room_type === 'campaign_application' ? 'Kampanja' : 'Direktna ponuda'}
              </Badge>
            </div>
          </div>
        </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Enhanced Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-gray-50/30 to-white">
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                  <div className="max-w-[70%] space-y-2">
                    <Skeleton className="h-12 w-48 rounded-lg" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
              ))}
            </div>
          ) : messages.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-muted-foreground py-12">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageCircle className="h-8 w-8 text-blue-500" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Počnite razgovor</h3>
                <p className="text-sm">Pošaljite prvu poruku da započnete komunikaciju!</p>
              </div>
            </div>
          ) : (
            messages.map((message, index) => {
              const showDate = index === 0 ||
                formatDate(message.created_at || '') !== formatDate(messages[index - 1]?.created_at || '');

              return (
                <div key={message.id}>
                  {showDate && (
                    <div className="text-center text-xs text-muted-foreground mb-4">
                      <div className="bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full inline-block border">
                        {formatDate(message.created_at || '')}
                      </div>
                    </div>
                  )}

                  <div className={`flex ${isMyMessage(message) ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[70%] ${isMyMessage(message) ? 'order-2' : 'order-1'}`}>
                      <div
                        className={`rounded-2xl px-4 py-3 shadow-sm ${
                          isMyMessage(message)
                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                            : 'bg-white border border-gray-200'
                        }`}
                      >
                        <p className="text-sm leading-relaxed">{message.message_text}</p>
                      </div>
                      <div className={`flex items-center gap-1 mt-1 ${
                        isMyMessage(message) ? 'justify-end' : 'justify-start'
                      }`}>
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <p className="text-xs text-muted-foreground">
                          {formatTime(message.created_at || '')}
                        </p>
                        {isMyMessage(message) && (
                          <CheckCircle2 className="h-3 w-3 text-blue-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Enhanced Message Input */}
        <div className="border-t bg-white p-4">
          <form onSubmit={handleSendMessage} className="flex gap-3">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Napišite poruku..."
              disabled={sending}
              className="flex-1 rounded-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
            <Button
              type="submit"
              disabled={!newMessage.trim() || sending}
              className="rounded-full w-12 h-12 p-0 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg"
            >
              {sending ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
