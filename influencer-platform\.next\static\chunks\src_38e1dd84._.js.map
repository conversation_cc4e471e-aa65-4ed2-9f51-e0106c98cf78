{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/profiles.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { Database } from './database.types';\n\ntype Profile = Database['public']['Tables']['profiles']['Row'];\ntype ProfileInsert = Database['public']['Tables']['profiles']['Insert'];\ntype ProfileUpdate = Database['public']['Tables']['profiles']['Update'];\n\ntype Influencer = Database['public']['Tables']['influencers']['Row'];\ntype InfluencerInsert = Database['public']['Tables']['influencers']['Insert'];\ntype InfluencerUpdate = Database['public']['Tables']['influencers']['Update'];\n\ntype Business = Database['public']['Tables']['businesses']['Row'];\ntype BusinessInsert = Database['public']['Tables']['businesses']['Insert'];\ntype BusinessUpdate = Database['public']['Tables']['businesses']['Update'];\n\n// Profile functions\nexport const getProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateProfile = async (userId: string, updates: ProfileUpdate) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const createProfile = async (profileData: ProfileInsert) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .insert(profileData)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Get public influencer profile by username\nexport const getPublicInfluencerProfile = async (username: string) => {\n  // Use RPC function to get influencer data\n  const { data: influencerData, error: influencerError } = await supabase\n    .rpc('get_influencers_with_details', {\n      search_term: username,\n      min_followers: 0,\n      max_followers: 999999999,\n      min_price: 0,\n      max_price: 999999,\n      platform_filter: '',\n      category_filter: '',\n      location_filter: '',\n      limit_count: 10\n    });\n\n  if (influencerError || !influencerData || influencerData.length === 0) {\n    return { data: null, error: influencerError || { message: 'Influencer not found' } };\n  }\n\n  // Find the exact username match\n  const exactMatch = influencerData.find(item => item.username === username);\n  if (!exactMatch) {\n    return { data: null, error: { message: 'Influencer not found' } };\n  }\n\n  const data = exactMatch;\n\n  // Transform data to match expected structure\n  const transformedData = {\n    id: data.id,\n    username: data.username,\n    full_name: data.full_name,\n    avatar_url: data.avatar_url,\n    bio: data.bio,\n    location: data.location,\n    created_at: data.created_at,\n    gender: data.gender,\n    age: data.age,\n    is_verified: data.is_verified,\n    platforms: [\n      ...(data.instagram_followers > 0 ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        handle: `@${data.username}`,\n        followers_count: data.instagram_followers,\n        is_verified: data.is_verified\n      }] : []),\n      ...(data.tiktok_followers > 0 ? [{\n        platform_id: 2,\n        platform_name: 'TikTok',\n        platform_icon: '🎵',\n        handle: `@${data.username}`,\n        followers_count: data.tiktok_followers,\n        is_verified: false\n      }] : []),\n      ...(data.youtube_subscribers > 0 ? [{\n        platform_id: 3,\n        platform_name: 'YouTube',\n        platform_icon: '📺',\n        handle: `@${data.username}`,\n        followers_count: data.youtube_subscribers,\n        is_verified: false\n      }] : [])\n    ],\n    categories: [], // TODO: Add categories when implemented\n    pricing: [\n      ...(data.price_per_post ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 1,\n        content_type_name: 'Post',\n        price: Number(data.price_per_post),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_story ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 2,\n        content_type_name: 'Story',\n        price: Number(data.price_per_story),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_reel ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 3,\n        content_type_name: 'Reel',\n        price: Number(data.price_per_reel),\n        currency: 'KM'\n      }] : [])\n    ],\n    portfolio_items: [], // TODO: Add portfolio items when implemented\n    total_followers: (data.instagram_followers || 0) +\n                    (data.tiktok_followers || 0) +\n                    (data.youtube_subscribers || 0),\n    min_price: Math.min(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0,\n    max_price: Math.max(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0\n  };\n\n  return { data: transformedData, error: null };\n};\n\nexport const upsertProfile = async (userId: string, updates: ProfileUpdate) => {\n  // First try to get existing profile\n  const { data: existingProfile } = await getProfile(userId);\n\n  if (existingProfile) {\n    // Profile exists, update it\n    return updateProfile(userId, updates);\n  } else {\n    // Profile doesn't exist, create it\n    const profileData: ProfileInsert = {\n      id: userId,\n      user_type: updates.user_type || 'influencer',\n      username: updates.username || null,\n      full_name: updates.full_name || null,\n      avatar_url: updates.avatar_url || null,\n      bio: updates.bio || null,\n      website_url: updates.website_url || null,\n      location: updates.location || null,\n    };\n    return createProfile(profileData);\n  }\n};\n\nexport const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {\n  let query = supabase\n    .from('profiles')\n    .select('id')\n    .eq('username', username);\n  \n  if (excludeUserId) {\n    query = query.neq('id', excludeUserId);\n  }\n  \n  const { data, error } = await query;\n  \n  if (error) return { available: false, error };\n  return { available: data.length === 0, error: null };\n};\n\n// Influencer functions\nexport const getInfluencer = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createInfluencer = async (influencerData: InfluencerInsert) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .insert(influencerData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateInfluencer = async (userId: string, updates: InfluencerUpdate) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const getInfluencers = async (filters?: {\n  niche?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  location?: string;\n  limit?: number;\n  offset?: number;\n}) => {\n  let query = supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `);\n\n  if (filters?.niche) {\n    query = query.ilike('niche', `%${filters.niche}%`);\n  }\n\n  if (filters?.minFollowers) {\n    query = query.gte('instagram_followers', filters.minFollowers);\n  }\n\n  if (filters?.maxFollowers) {\n    query = query.lte('instagram_followers', filters.maxFollowers);\n  }\n\n  if (filters?.location) {\n    query = query.eq('profiles.location', filters.location);\n  }\n\n  if (filters?.limit) {\n    query = query.limit(filters.limit);\n  }\n\n  if (filters?.offset) {\n    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n};\n\n// Business functions\nexport const getBusiness = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createBusiness = async (businessData: BusinessInsert) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .insert(businessData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateBusiness = async (userId: string, updates: BusinessUpdate) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Category functions\nexport const getCategories = async () => {\n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .order('name');\n\n  return { data, error };\n};\n\nexport const getInfluencerCategories = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_categories')\n    .select(`\n      category_id,\n      is_primary,\n      categories (*)\n    `)\n    .eq('influencer_id', influencerId);\n\n  return { data, error };\n};\n\nexport const updateInfluencerCategories = async (influencerId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('influencer_categories')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map((categoryId, index) => ({\n      influencer_id: influencerId,\n      category_id: categoryId,\n      is_primary: index === 0 // First category is primary\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const getBusinessTargetCategories = async (businessId: string) => {\n  const { data, error } = await supabase\n    .from('business_target_categories')\n    .select(`\n      category_id,\n      categories (*)\n    `)\n    .eq('business_id', businessId);\n\n  return { data, error };\n};\n\nexport const updateBusinessTargetCategories = async (businessId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('business_target_categories')\n    .delete()\n    .eq('business_id', businessId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map(categoryId => ({\n      business_id: businessId,\n      category_id: categoryId\n    }));\n\n    const { data, error } = await supabase\n      .from('business_target_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\n// Combined profile functions\nexport const getFullProfile = async (userId: string) => {\n  const { data: profile, error: profileError } = await getProfile(userId);\n  \n  if (profileError || !profile) {\n    return { data: null, error: profileError };\n  }\n\n  if (profile.user_type === 'influencer') {\n    const { data: influencer, error: influencerError } = await getInfluencer(userId);\n    return { \n      data: influencer ? { ...profile, influencer } : profile, \n      error: influencerError \n    };\n  } else if (profile.user_type === 'business') {\n    const { data: business, error: businessError } = await getBusiness(userId);\n    return { \n      data: business ? { ...profile, business } : profile, \n      error: businessError \n    };\n  }\n\n  return { data: profile, error: null };\n};\n\n// Upload avatar function\nexport const uploadAvatar = async (userId: string, file: File) => {\n  const fileExt = file.name.split('.').pop();\n  const fileName = `${userId}-${Math.random()}.${fileExt}`;\n  const filePath = `avatars/${fileName}`;\n\n  const { error: uploadError } = await supabase.storage\n    .from('avatars')\n    .upload(filePath, file);\n\n  if (uploadError) {\n    return { data: null, error: uploadError };\n  }\n\n  const { data } = supabase.storage\n    .from('avatars')\n    .getPublicUrl(filePath);\n\n  // Update profile with new avatar URL\n  const { error: updateError } = await updateProfile(userId, {\n    avatar_url: data.publicUrl,\n  });\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: data.publicUrl, error: null };\n};\n\n// Platform and Pricing functions\nexport const getInfluencerPlatforms = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platforms')\n    .select(`\n      *,\n      platforms (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_active', true);\n\n  return { data, error };\n};\n\nexport const getInfluencerPricing = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platform_pricing')\n    .select(`\n      *,\n      platforms (*),\n      content_types (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_available', true);\n\n  return { data, error };\n};\n\nexport const updateInfluencerPlatforms = async (influencerId: string, platforms: any[]) => {\n  // First, delete existing platforms\n  await supabase\n    .from('influencer_platforms')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new platforms\n  if (platforms.length > 0) {\n    const platformData = platforms.map(platform => ({\n      influencer_id: influencerId,\n      platform_id: platform.platform_id,\n      handle: platform.handle || null,\n      followers_count: platform.followers_count || 0,\n      is_verified: false,\n      is_active: true\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platforms')\n      .insert(platformData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const updateInfluencerPricing = async (influencerId: string, pricing: any[]) => {\n  // First, delete existing pricing\n  await supabase\n    .from('influencer_platform_pricing')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new pricing\n  if (pricing.length > 0) {\n    const pricingData = pricing.map(price => ({\n      influencer_id: influencerId,\n      platform_id: price.platform_id,\n      content_type_id: price.content_type_id,\n      price: price.price,\n      currency: 'KM',\n      is_available: price.is_available !== false\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platform_pricing')\n      .insert(pricingData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgBO,MAAM,aAAa,OAAO;IAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,6BAA6B,OAAO;IAC/C,0CAA0C;IAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACpE,GAAG,CAAC,gCAAgC;QACnC,aAAa;QACb,eAAe;QACf,eAAe;QACf,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;IACf;IAEF,IAAI,mBAAmB,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO,mBAAmB;gBAAE,SAAS;YAAuB;QAAE;IACrF;IAEA,gCAAgC;IAChC,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACjE,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAuB;QAAE;IAClE;IAEA,MAAM,OAAO;IAEb,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,KAAK,KAAK,GAAG;QACb,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,aAAa,KAAK,WAAW;QAC7B,WAAW;eACL,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAiB,OAAd,KAAK,QAAQ;oBACzB,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa,KAAK,WAAW;gBAC/B;aAAE,GAAG,EAAE;eACH,KAAK,gBAAgB,GAAG,IAAI;gBAAC;oBAC/B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAiB,OAAd,KAAK,QAAQ;oBACzB,iBAAiB,KAAK,gBAAgB;oBACtC,aAAa;gBACf;aAAE,GAAG,EAAE;eACH,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAiB,OAAd,KAAK,QAAQ;oBACzB,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa;gBACf;aAAE,GAAG,EAAE;SACR;QACD,YAAY,EAAE;QACd,SAAS;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,eAAe,GAAG;gBAAC;oBAC1B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,eAAe;oBAClC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;SACR;QACD,iBAAiB,EAAE;QACnB,iBAAiB,CAAC,KAAK,mBAAmB,IAAI,CAAC,IAC/B,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAC3B,CAAC,KAAK,mBAAmB,IAAI,CAAC;QAC9C,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;QACL,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;IACP;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,oCAAoC;IACpC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,WAAW;IAEnD,IAAI,iBAAiB;QACnB,4BAA4B;QAC5B,OAAO,cAAc,QAAQ;IAC/B,OAAO;QACL,mCAAmC;QACnC,MAAM,cAA6B;YACjC,IAAI;YACJ,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;YAChC,YAAY,QAAQ,UAAU,IAAI;YAClC,KAAK,QAAQ,GAAG,IAAI;YACpB,aAAa,QAAQ,WAAW,IAAI;YACpC,UAAU,QAAQ,QAAQ,IAAI;QAChC;QACA,OAAO,cAAc;IACvB;AACF;AAEO,MAAM,yBAAyB,OAAO,UAAkB;IAC7D,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY;IAElB,IAAI,eAAe;QACjB,QAAQ,MAAM,GAAG,CAAC,MAAM;IAC1B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO,OAAO;QAAE,WAAW;QAAO;IAAM;IAC5C,OAAO;QAAE,WAAW,KAAK,MAAM,KAAK;QAAG,OAAO;IAAK;AACrD;AAGO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAE,wCAIR,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,gBACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IAQnC,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAE;IAKX,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;QAClB,QAAQ,MAAM,KAAK,CAAC,SAAS,AAAC,IAAiB,OAAd,QAAQ,KAAK,EAAC;IACjD;IAEA,IAAI,oBAAA,8BAAA,QAAS,YAAY,EAAE;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,oBAAA,8BAAA,QAAS,YAAY,EAAE;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;QACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB,QAAQ,QAAQ;IACxD;IAEA,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;QAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;IACnC;IAEA,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAI;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAE,wCAIR,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO,QAAgB;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAE,uEAKR,EAAE,CAAC,iBAAiB;IAEvB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,6BAA6B,OAAO,cAAsB;IACrE,oCAAoC;IACpC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,yBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,YAAY,QAAU,CAAC;gBAC3D,eAAe;gBACf,aAAa;gBACb,YAAY,UAAU,EAAE,4BAA4B;YACtD,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAE,oDAIR,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iCAAiC,OAAO,YAAoB;IACvE,oCAAoC;IACpC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,8BACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBAClD,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,WAAW;IAEhE,IAAI,gBAAgB,CAAC,SAAS;QAC5B,OAAO;YAAE,MAAM;YAAM,OAAO;QAAa;IAC3C;IAEA,IAAI,QAAQ,SAAS,KAAK,cAAc;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAAc;QACzE,OAAO;YACL,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE;YAAW,IAAI;YAChD,OAAO;QACT;IACF,OAAO,IAAI,QAAQ,SAAS,KAAK,YAAY;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YAAY;QACnE,OAAO;YACL,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE;YAAS,IAAI;YAC5C,OAAO;QACT;IACF;IAEA,OAAO;QAAE,MAAM;QAAS,OAAO;IAAK;AACtC;AAGO,MAAM,eAAe,OAAO,QAAgB;IACjD,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;IACxC,MAAM,WAAW,AAAC,GAAY,OAAV,QAAO,KAAoB,OAAjB,KAAK,MAAM,IAAG,KAAW,OAAR;IAC/C,MAAM,WAAW,AAAC,WAAmB,OAAT;IAE5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU;IAEpB,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;IAEhB,qCAAqC;IACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,QAAQ;QACzD,YAAY,KAAK,SAAS;IAC5B;IAEA,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM,KAAK,SAAS;QAAE,OAAO;IAAK;AAC7C;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAE,yCAIR,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,aAAa;IAEnB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,uBAAuB,OAAO;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAE,mEAKR,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,gBAAgB;IAEtB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,4BAA4B,OAAO,cAAsB;IACpE,mCAAmC;IACnC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,wBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,4BAA4B;IAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC9C,eAAe;gBACf,aAAa,SAAS,WAAW;gBACjC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,SAAS,eAAe,IAAI;gBAC7C,aAAa;gBACb,WAAW;YACb,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,0BAA0B,OAAO,cAAsB;IAClE,iCAAiC;IACjC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,+BACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,0BAA0B;IAC1B,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACxC,eAAe;gBACf,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;gBACtC,OAAO,MAAM,KAAK;gBAClB,UAAU;gBACV,cAAc,MAAM,YAAY,KAAK;YACvC,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,aACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/dashboard/DashboardSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  User,\n  Settings,\n  Building2,\n  Users,\n  FileText,\n  MessageCircle,\n  DollarSign,\n  LogOut,\n  ChevronLeft,\n  ChevronRight,\n  Send,\n  Inbox\n} from 'lucide-react';\nimport { useState } from 'react';\n\ninterface SidebarItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\ninterface DashboardSidebarProps {\n  userType: 'influencer' | 'business';\n  className?: string;\n}\n\nexport function DashboardSidebar({ userType, className }: DashboardSidebarProps) {\n  const pathname = usePathname();\n  const router = useRouter();\n  const { signOut } = useAuth();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  // Navigacija za influencer korisnike\n  const influencerNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      description: 'Pregled aktivnosti i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      description: 'Dostupne kampanje'\n    },\n    {\n      name: 'Ponude i aplikacije',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      description: 'Direktne ponude i aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa brendovima'\n    },\n    {\n      name: 'Zarade',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Historija zarada'\n    }\n  ];\n\n  // Navigacija za biznis korisnike\n  const businessNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      description: 'Pregled kampanja i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: Building2,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Moje kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      description: 'Upravljanje kampanjama'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: FileText,\n      description: 'Aplikacije na kampanje'\n    },\n    {\n      name: 'Moje ponude',\n      href: '/dashboard/biznis/offers',\n      icon: Send,\n      description: 'Direktne ponude influencerima'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: Users,\n      description: 'Pronađi influencere'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa influencerima'\n    }\n  ];\n\n  const navigation = userType === 'influencer' ? influencerNavigation : businessNavigation;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  return (\n    <div className={cn(\n      \"flex flex-col h-full bg-card border-r border-border transition-all duration-300\",\n      isCollapsed ? \"w-16\" : \"w-64\",\n      className\n    )}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-border\">\n        {!isCollapsed && (\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-lg\">🔗</span>\n            </div>\n            <span className=\"text-lg font-bold text-foreground\">InfluConnect</span>\n          </div>\n        )}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 p-0\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* User Type Badge */}\n      {!isCollapsed && (\n        <div className=\"px-4 py-2\">\n          <div className={cn(\n            \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium\",\n            userType === 'influencer' \n              ? \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\"\n              : \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\"\n          )}>\n            {userType === 'influencer' ? (\n              <>\n                <User className=\"w-3 h-3 mr-1\" />\n                Influencer\n              </>\n            ) : (\n              <>\n                <Building2 className=\"w-3 h-3 mr-1\" />\n                Biznis\n              </>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-2 py-4 space-y-1\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n          \n          return (\n            <Link key={item.name} href={item.href}>\n              <div className={cn(\n                \"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors\",\n                isActive\n                  ? \"bg-primary text-primary-foreground\"\n                  : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n              )}>\n                <item.icon className={cn(\n                  \"flex-shrink-0 h-5 w-5\",\n                  isCollapsed ? \"mx-auto\" : \"mr-3\"\n                )} />\n                {!isCollapsed && (\n                  <div className=\"flex-1\">\n                    <div className=\"font-medium\">{item.name}</div>\n                    {item.description && (\n                      <div className=\"text-xs opacity-75 mt-0.5\">\n                        {item.description}\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-2 border-t border-border\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleSignOut}\n          className={cn(\n            \"w-full justify-start text-muted-foreground hover:text-foreground\",\n            isCollapsed && \"justify-center\"\n          )}\n        >\n          <LogOut className={cn(\"h-4 w-4\", !isCollapsed && \"mr-2\")} />\n          {!isCollapsed && \"Odjavi se\"}\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AAtBA;;;;;;;;AAoCO,SAAS,iBAAiB,KAA8C;QAA9C,EAAE,QAAQ,EAAE,SAAS,EAAyB,GAA9C;;IAC/B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qCAAqC;IACrC,MAAM,uBAAsC;QAC1C;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,iCAAiC;IACjC,MAAM,qBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,aAAa,aAAa,eAAe,uBAAuB;IAEtE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,mFACA,cAAc,SAAS,QACvB;;0BAGA,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;0CAE9D,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAGxD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAExB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAM5B,CAAC,6BACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qEACA,aAAa,eACT,kEACA;8BAEH,aAAa,6BACZ;;0CACE,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;qDAInC;;0CACE,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAShD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oBAE3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAAiB,MAAM,KAAK,IAAI;kCACnC,cAAA,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,sFACA,WACI,uCACA;;8CAEJ,6LAAC,KAAK,IAAI;oCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,yBACA,cAAc,YAAY;;;;;;gCAE3B,CAAC,6BACA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAe,KAAK,IAAI;;;;;;wCACtC,KAAK,WAAW,kBACf,6LAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uBAhBlB,KAAK,IAAI;;;;;gBAwBxB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA,eAAe;;sCAGjB,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,eAAe;;;;;;wBAChD,CAAC,eAAe;;;;;;;;;;;;;;;;;;AAK3B;GA3NgB;;QACG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACJ,kIAAA,CAAA,UAAO;;;KAHb", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,KAIoC;QAJpC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD,GAJpC;IAKlB,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,KAIoD;QAJpD,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE,GAJpD;IAKjB,qBACE,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/notifications.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\nexport interface Notification {\n  id: string;\n  user_id: string;\n  type: string;\n  title: string;\n  message: string;\n  data: Record<string, any>;\n  read: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport type NotificationType = \n  | 'offer_received'\n  | 'offer_accepted'\n  | 'offer_rejected'\n  | 'campaign_application'\n  | 'campaign_accepted'\n  | 'campaign_rejected'\n  | 'message_received'\n  | 'payment_received';\n\n// Create a new notification\nexport async function createNotification(\n  userId: string,\n  type: NotificationType,\n  title: string,\n  message: string,\n  data: Record<string, any> = {}\n) {\n  const { data: notification, error } = await supabase.rpc('create_notification', {\n    p_user_id: userId,\n    p_type: type,\n    p_title: title,\n    p_message: message,\n    p_data: data\n  });\n\n  if (error) {\n    console.error('Error creating notification:', error);\n    return { data: null, error };\n  }\n\n  return { data: notification, error: null };\n}\n\n// Get user notifications\nexport async function getUserNotifications(userId?: string, limit = 50) {\n  let query = supabase\n    .from('notifications')\n    .select('*')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (userId) {\n    query = query.eq('user_id', userId);\n  }\n\n  const { data, error } = await query;\n\n  if (error) {\n    console.error('Error fetching notifications:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark notification as read\nexport async function markNotificationAsRead(notificationId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('id', notificationId)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error marking notification as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark all notifications as read for user\nexport async function markAllNotificationsAsRead(userId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error marking all notifications as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Get unread notification count\nexport async function getUnreadNotificationCount(userId: string) {\n  const { count, error } = await supabase\n    .from('notifications')\n    .select('*', { count: 'exact', head: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error getting unread count:', error);\n    return { count: 0, error };\n  }\n\n  return { count: count || 0, error: null };\n}\n\n// Delete notification\nexport async function deleteNotification(notificationId: string) {\n  const { error } = await supabase\n    .from('notifications')\n    .delete()\n    .eq('id', notificationId);\n\n  if (error) {\n    console.error('Error deleting notification:', error);\n    return { error };\n  }\n\n  return { error: null };\n}\n\n// Helper functions for specific notification types\n\nexport async function notifyOfferReceived(\n  influencerId: string,\n  businessName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    influencerId,\n    'offer_received',\n    'Nova direktna ponuda',\n    `${businessName} vam je poslao ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, business_name: businessName }\n  );\n}\n\nexport async function notifyOfferAccepted(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_accepted',\n    'Ponuda prihvaćena',\n    `${influencerName} je prihvatio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyOfferRejected(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_rejected',\n    'Ponuda odbijena',\n    `${influencerName} je odbio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignApplication(\n  businessId: string,\n  influencerName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    businessId,\n    'campaign_application',\n    'Nova aplikacija na kampanju',\n    `${influencerName} se prijavio na kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignAccepted(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_accepted',\n    'Aplikacija prihvaćena',\n    `${businessName} je prihvatio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyCampaignRejected(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_rejected',\n    'Aplikacija odbijena',\n    `${businessName} je odbio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyMessageReceived(\n  userId: string,\n  senderName: string,\n  conversationId: string\n) {\n  return createNotification(\n    userId,\n    'message_received',\n    'Nova poruka',\n    `${senderName} vam je poslao novu poruku`,\n    { conversation_id: conversationId, sender_name: senderName }\n  );\n}\n\nexport async function notifyPaymentReceived(\n  influencerId: string,\n  amount: number,\n  currency: string,\n  campaignTitle: string\n) {\n  return createNotification(\n    influencerId,\n    'payment_received',\n    'Plaćanje primljeno',\n    `Primili ste plaćanje od ${amount} ${currency} za kampanju: \"${campaignTitle}\"`,\n    { amount, currency, campaign_title: campaignTitle }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAyBO,eAAe,mBACpB,MAAc,EACd,IAAsB,EACtB,KAAa,EACb,OAAe;QACf,OAAA,iEAA4B,CAAC;IAE7B,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,uBAAuB;QAC9E,WAAW;QACX,QAAQ;QACR,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE,MAAM;QAAc,OAAO;IAAK;AAC3C;AAGO,eAAe,qBAAqB,MAAe;QAAE,QAAA,iEAAQ;IAClE,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,WAAW;IAC9B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,uBAAuB,cAAsB;IACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,MAAM,gBACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACpC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;QAAE,OAAO;QAAS,MAAM;IAAK,GACzC,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;YAAG;QAAM;IAC3B;IAEA,OAAO;QAAE,OAAO,SAAS;QAAG,OAAO;IAAK;AAC1C;AAGO,eAAe,mBAAmB,cAAsB;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE;QAAM;IACjB;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAIO,eAAe,oBACpB,YAAoB,EACpB,YAAoB,EACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,cACA,kBACA,wBACA,AAAC,GAAyC,OAAvC,cAAa,4BAAqC,OAAX,YAAW,MACrD;QAAE,UAAU;QAAS,eAAe;IAAa;AAErD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,qBACA,AAAC,GAA+C,OAA7C,gBAAe,gCAAyC,OAAX,YAAW,MAC3D;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,mBACA,AAAC,GAA2C,OAAzC,gBAAe,4BAAqC,OAAX,YAAW,MACvD;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,0BACpB,UAAkB,EAClB,cAAsB,EACtB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,YACA,wBACA,+BACA,AAAC,GAA8C,OAA5C,gBAAe,+BAA2C,OAAd,eAAc,MAC7D;QAAE,gBAAgB;QAAe,iBAAiB;IAAe;AAErE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,yBACA,AAAC,GAA6D,OAA3D,cAAa,gDAA4D,OAAd,eAAc,MAC5E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,uBACA,AAAC,GAAyD,OAAvD,cAAa,4CAAwD,OAAd,eAAc,MACxE;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,sBACpB,MAAc,EACd,UAAkB,EAClB,cAAsB;IAEtB,OAAO,mBACL,QACA,oBACA,eACA,AAAC,GAAa,OAAX,YAAW,+BACd;QAAE,iBAAiB;QAAgB,aAAa;IAAW;AAE/D;AAEO,eAAe,sBACpB,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,aAAqB;IAErB,OAAO,mBACL,cACA,oBACA,sBACA,AAAC,2BAAoC,OAAV,QAAO,KAA6B,OAA1B,UAAS,mBAA+B,OAAd,eAAc,MAC7E;QAAE;QAAQ;QAAU,gBAAgB;IAAc;AAEtD", "debugId": null}}, {"offset": {"line": 1551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuLabel,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n  DropdownMenuItem,\n} from '@/components/ui/dropdown-menu';\nimport { \n  Bell, \n  Check, \n  CheckCheck,\n  Inbox,\n  MessageCircle,\n  DollarSign,\n  FileText,\n  Building2,\n  User\n} from 'lucide-react';\nimport { \n  getUserNotifications, \n  markNotificationAsRead, \n  markAllNotificationsAsRead,\n  getUnreadNotificationCount,\n  type Notification \n} from '@/lib/notifications';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { formatDistanceToNow } from 'date-fns';\nimport { hr } from 'date-fns/locale';\nimport { toast } from 'sonner';\nimport Link from 'next/link';\n\nexport function NotificationDropdown() {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    if (user) {\n      loadNotifications();\n      loadUnreadCount();\n    }\n  }, [user]);\n\n  const loadNotifications = async () => {\n    if (!user) return;\n    \n    setIsLoading(true);\n    try {\n      const { data, error } = await getUserNotifications(user.id, 20);\n      if (error) {\n        console.error('Error loading notifications:', error);\n      } else {\n        setNotifications(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading notifications:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadUnreadCount = async () => {\n    if (!user) return;\n    \n    try {\n      const { count, error } = await getUnreadNotificationCount(user.id);\n      if (error) {\n        console.error('Error loading unread count:', error);\n      } else {\n        setUnreadCount(count);\n      }\n    } catch (error) {\n      console.error('Error loading unread count:', error);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId: string) => {\n    try {\n      const { error } = await markNotificationAsRead(notificationId);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacije');\n      } else {\n        setNotifications(prev => \n          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n      toast.error('Greška pri označavanju notifikacije');\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    if (!user) return;\n    \n    try {\n      const { error } = await markAllNotificationsAsRead(user.id);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacija');\n      } else {\n        setNotifications(prev => prev.map(n => ({ ...n, read: true })));\n        setUnreadCount(0);\n        toast.success('Sve notifikacije su označene kao pročitane');\n      }\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      toast.error('Greška pri označavanju notifikacija');\n    }\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        return <Inbox className=\"h-4 w-4\" />;\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return <FileText className=\"h-4 w-4\" />;\n      case 'message_received':\n        return <MessageCircle className=\"h-4 w-4\" />;\n      case 'payment_received':\n        return <DollarSign className=\"h-4 w-4\" />;\n      default:\n        return <Bell className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getNotificationLink = (notification: Notification) => {\n    switch (notification.type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        if (notification.data.offer_id) {\n          return `/dashboard/influencer/offers/${notification.data.offer_id}`;\n        }\n        return '/dashboard/influencer/offers';\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return '/dashboard/campaigns';\n      case 'message_received':\n        return '/dashboard/messages';\n      default:\n        return '/dashboard';\n    }\n  };\n\n  if (!user) return null;\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-80\">\n        <DropdownMenuLabel className=\"flex items-center justify-between p-4\">\n          <h3 className=\"font-semibold\">Notifikacije</h3>\n          {unreadCount > 0 && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleMarkAllAsRead}\n              className=\"text-xs\"\n            >\n              <CheckCheck className=\"h-3 w-3 mr-1\" />\n              Označi sve\n            </Button>\n          )}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <ScrollArea className=\"h-96\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center p-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\n            </div>\n          ) : notifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center p-8 text-center\">\n              <Bell className=\"h-8 w-8 text-muted-foreground mb-2\" />\n              <p className=\"text-sm text-muted-foreground\">Nema novih notifikacija</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1\">\n              {notifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${\n                    notification.read ? 'border-transparent' : 'border-primary'\n                  }`}\n                >\n                  <Link \n                    href={getNotificationLink(notification)}\n                    onClick={() => {\n                      if (!notification.read) {\n                        handleMarkAsRead(notification.id);\n                      }\n                      setIsOpen(false);\n                    }}\n                    className=\"block\"\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      <div className=\"flex-shrink-0 mt-0.5\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <h4 className={`text-sm font-medium ${\n                            notification.read ? 'text-muted-foreground' : 'text-foreground'\n                          }`}>\n                            {notification.title}\n                          </h4>\n                          {!notification.read && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                e.stopPropagation();\n                                handleMarkAsRead(notification.id);\n                              }}\n                              className=\"h-6 w-6 p-0 ml-2\"\n                            >\n                              <Check className=\"h-3 w-3\" />\n                            </Button>\n                          )}\n                        </div>\n                        <p className={`text-xs mt-1 ${\n                          notification.read ? 'text-muted-foreground' : 'text-muted-foreground'\n                        }`}>\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          {formatDistanceToNow(new Date(notification.created_at), { \n                            addSuffix: true, \n                            locale: hr \n                          })}\n                        </p>\n                      </div>\n                    </div>\n                  </Link>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n        \n        {notifications.length > 0 && (\n          <>\n            <DropdownMenuSeparator />\n            <div className=\"p-2\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                <Link href=\"/dashboard/notifications\">\n                  Pogledaj sve notifikacije\n                </Link>\n              </Button>\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;AACA;AACA;AACA;;;AApCA;;;;;;;;;;;;;AAsCO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,MAAM;gBACR;gBACA;YACF;QACF;yCAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5D,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;YAChD,OAAO;gBACL,iBAAiB,QAAQ,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YACjE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,OAAO;gBACL,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE;YAC/C,IAAI,OAAO;gBACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,IAAI;gBAEjE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YAC1D,IAAI,OAAO;gBACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,CAAC;gBAC5D,eAAe;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,aAAa,IAAI;YACvB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE;oBAC9B,OAAO,AAAC,gCAA0D,OAA3B,aAAa,IAAI,CAAC,QAAQ;gBACnE;gBACA,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAKpC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;4BAC7B,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK7C,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,cAAc,MAAM,KAAK,kBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;oCAEC,WAAW,AAAC,sDAEX,OADC,aAAa,IAAI,GAAG,uBAAuB;8CAG7C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,oBAAoB;wCAC1B,SAAS;4CACP,IAAI,CAAC,aAAa,IAAI,EAAE;gDACtB,iBAAiB,aAAa,EAAE;4CAClC;4CACA,UAAU;wCACZ;wCACA,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,oBAAoB,aAAa,IAAI;;;;;;8DAExC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAW,AAAC,uBAEf,OADC,aAAa,IAAI,GAAG,0BAA0B;8EAE7C,aAAa,KAAK;;;;;;gEAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,iBAAiB,aAAa,EAAE;oEAClC;oEACA,WAAU;8EAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIvB,6LAAC;4DAAE,WAAW,AAAC,gBAEd,OADC,aAAa,IAAI,GAAG,0BAA0B;sEAE7C,aAAa,OAAO;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,UAAU,GAAG;gEACtD,WAAW;gEACX,QAAQ,8IAAA,CAAA,KAAE;4DACZ;;;;;;;;;;;;;;;;;;;;;;;mCAlDH,aAAa,EAAE;;;;;;;;;;;;;;;oBA6D7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC1D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD;GArPgB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 2040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getProfile } from '@/lib/profiles';\nimport { DashboardSidebar } from './DashboardSidebar';\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown';\nimport { Loader2 } from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  requiredUserType?: 'influencer' | 'business';\n}\n\nexport function DashboardLayout({ children, requiredUserType }: DashboardLayoutProps) {\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [profile, setProfile] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!user) {\n      router.push('/prijava');\n      return;\n    }\n\n    loadProfile();\n  }, [user, authLoading, router]);\n\n  const loadProfile = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await getProfile(user!.id);\n\n      if (error) {\n        console.error('Profile loading error:', error);\n        if (error.message && error.message.includes('No rows')) {\n          router.push('/profil/kreiranje');\n          return;\n        }\n        setError('Greška pri učitavanju profila');\n        return;\n      }\n\n      if (!data) {\n        router.push('/profil/kreiranje');\n        return;\n      }\n\n      setProfile(data);\n\n      // Provjeri da li korisnik ima pravo pristupa ovoj stranici\n      if (requiredUserType && data.user_type !== requiredUserType) {\n        // Preusmjeri na odgovarajući dashboard\n        if (data.user_type === 'influencer') {\n          router.push('/dashboard/influencer');\n        } else if (data.user_type === 'business') {\n          router.push('/dashboard/biznis');\n        }\n        return;\n      }\n    } catch (err) {\n      console.error('Unexpected error in loadProfile:', err);\n      setError('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <p className=\"text-muted-foreground\">Učitavanje...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-foreground mb-2\">Greška</h2>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90\"\n          >\n            Pokušaj ponovo\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // No profile state\n  if (!profile) {\n    return null; // Router redirect will handle this\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background flex\">\n      {/* Sidebar */}\n      <DashboardSidebar userType={profile.user_type} />\n      \n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Header */}\n        <header className=\"bg-card border-b border-border px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-foreground\">\n                {profile.user_type === 'influencer' ? 'Influencer Dashboard' : 'Biznis Dashboard'}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                Dobrodošli, {profile.full_name || profile.username}\n              </p>\n            </div>\n            \n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <NotificationDropdown />\n              {profile.avatar_url && (\n                <img\n                  src={profile.avatar_url}\n                  alt={profile.username}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              )}\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-foreground\">\n                  {profile.full_name || profile.username}\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  @{profile.username}\n                </p>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAeO,SAAS,gBAAgB,KAAoD;QAApD,EAAE,QAAQ,EAAE,gBAAgB,EAAwB,GAApD;;IAC9B,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,aAAa;YAEjB,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA;QACF;oCAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,KAAM,EAAE;YAEjD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACtD,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,WAAW;YAEX,2DAA2D;YAC3D,IAAI,oBAAoB,KAAK,SAAS,KAAK,kBAAkB;gBAC3D,uCAAuC;gBACvC,IAAI,KAAK,SAAS,KAAK,cAAc;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,SAAS,KAAK,YAAY;oBACxC,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,mBAAmB;IACnB,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,mCAAmC;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,sJAAA,CAAA,mBAAgB;gBAAC,UAAU,QAAQ,SAAS;;;;;;0BAG7C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,QAAQ,SAAS,KAAK,eAAe,yBAAyB;;;;;;sDAEjE,6LAAC;4CAAE,WAAU;;gDAAwB;gDACtB,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8JAAA,CAAA,uBAAoB;;;;;wCACpB,QAAQ,UAAU,kBACjB,6LAAC;4CACC,KAAK,QAAQ,UAAU;4CACvB,KAAK,QAAQ,QAAQ;4CACrB,WAAU;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;8DAExC,6LAAC;oDAAE,WAAU;;wDAAgC;wDACzC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA/IgB;;QACyB,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 2353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,6JAAA,CAAA,aAAgB,OAGrC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 2580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/chat-permissions.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase';\n\nexport interface ChatPermission {\n  id: string;\n  business_id: string;\n  influencer_id: string;\n  offer_id: string | null;\n  campaign_application_id: string | null;\n  business_approved: boolean | null;\n  influencer_approved: boolean | null;\n  chat_enabled: boolean | null;\n  created_at: string | null;\n  updated_at: string | null;\n}\n\n/**\n * Create or update chat permission for direct offer\n */\nexport async function upsertOfferChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_offer_id: offerId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting offer chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Create or update chat permission for campaign application\n */\nexport async function upsertApplicationChatPermission(\n  businessId: string,\n  influencerId: string,\n  applicationId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_campaign_application_id: applicationId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting application chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Check if chat is enabled between business and influencer for specific offer/application\n */\nexport async function isChatEnabled(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<boolean> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('chat_enabled')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found, chat not enabled\n      return false;\n    }\n    console.error('Error checking chat permission:', error);\n    throw error;\n  }\n\n  return data?.chat_enabled || false;\n}\n\n/**\n * Get chat permission details\n */\nexport async function getChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<ChatPermission | null> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('*')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found\n      return null;\n    }\n    console.error('Error getting chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Approve chat from business side\n */\nexport async function approveBusinessChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ business_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving business chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Approve chat from influencer side\n */\nexport async function approveInfluencerChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ influencer_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving influencer chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Get all chat permissions for a user (business or influencer)\n */\nexport async function getUserChatPermissions(userId: string): Promise<ChatPermission[]> {\n  const { data, error } = await supabase\n    .from('chat_permissions')\n    .select('*')\n    .or(`business_id.eq.${userId},influencer_id.eq.${userId}`)\n    .order('created_at', { ascending: false });\n\n  if (error) {\n    console.error('Error getting user chat permissions:', error);\n    throw error;\n  }\n\n  return data || [];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAkBO,eAAe,0BACpB,UAAkB,EAClB,YAAoB,EACpB,OAAe;QACf,mBAAA,iEAA4B,OAC5B,qBAAA,iEAA8B;IAE9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,gCACpB,UAAkB,EAClB,YAAoB,EACpB,aAAqB;QACrB,mBAAA,iEAA4B,OAC5B,qBAAA,iEAA8B;IAE9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,2BAA2B;QAC3B,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,cACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,gBACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,+CAA+C;YAC/C,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;IAEA,OAAO,CAAA,iBAAA,2BAAA,KAAM,YAAY,KAAI;AAC/B;AAKO,eAAe,kBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,6BAA6B;YAC7B,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,oBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,mBAAmB;IAAK,GACjC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,eAAe,sBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,qBAAqB;IAAK,GACnC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,MAAc;IACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,AAAC,kBAA4C,OAA3B,QAAO,sBAA2B,OAAP,SAChD,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;IAEA,OAAO,QAAQ,EAAE;AACnB", "debugId": null}}, {"offset": {"line": 2714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/chat.ts"], "sourcesContent": ["import { supabase } from './supabase';\r\nimport { isChatEnabled, upsertApplicationChatPermission, upsertOfferChatPermission } from './chat-permissions';\r\n\r\nexport interface ChatRoom {\r\n  id: string;\r\n  business_id: string;\r\n  influencer_id: string;\r\n  campaign_application_id: string | null;\r\n  offer_id: string | null;\r\n  room_title: string;\r\n  room_type: string;\r\n  created_at: string | null;\r\n  updated_at: string | null;\r\n  last_message_at: string | null;\r\n\r\n  // Joined data\r\n  business_profile?: {\r\n    full_name: string;\r\n    username: string;\r\n    avatar_url: string | null;\r\n  };\r\n  influencer_profile?: {\r\n    full_name: string;\r\n    username: string;\r\n    avatar_url: string | null;\r\n  };\r\n  unread_count?: number;\r\n}\r\n\r\nexport interface ChatMessage {\r\n  id: string;\r\n  room_id: string;\r\n  sender_id: string;\r\n  sender_type: string;\r\n  message_text: string | null;\r\n  file_url: string | null;\r\n  file_name: string | null;\r\n  file_type: string | null;\r\n  file_size: number | null;\r\n  created_at: string | null;\r\n  read_at: string | null;\r\n  edited_at: string | null;\r\n\r\n  // Joined data\r\n  sender_profile?: {\r\n    full_name: string;\r\n    username: string;\r\n    avatar_url: string | null;\r\n  };\r\n}\r\n\r\nexport interface ChatParticipant {\r\n  id: string;\r\n  room_id: string;\r\n  user_id: string;\r\n  user_type: 'business' | 'influencer';\r\n  joined_at: string;\r\n  last_read_at: string | null;\r\n  is_active: boolean;\r\n}\r\n\r\n/**\r\n * Get or create a chat room for a campaign application\r\n */\r\nexport async function getOrCreateChatRoomForApplication(\r\n  campaignApplicationId: string\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    // First, get the campaign application details\r\n    const { data: application, error: appError } = await supabase\r\n      .from('campaign_applications')\r\n      .select(`\r\n        id,\r\n        campaign_id,\r\n        influencer_id,\r\n        campaigns!inner(\r\n          id,\r\n          title,\r\n          business_id\r\n        )\r\n      `)\r\n      .eq('id', campaignApplicationId)\r\n      .single();\r\n\r\n    if (appError || !application) {\r\n      return { data: null, error: appError || 'Application not found' };\r\n    }\r\n\r\n    const businessId = application.campaigns.business_id;\r\n    const influencerId = application.influencer_id;\r\n    const campaignTitle = application.campaigns.title;\r\n\r\n    // Check if chat is enabled via permissions\r\n    const chatEnabled = await isChatEnabled(businessId, influencerId, undefined, campaignApplicationId);\r\n    if (!chatEnabled) {\r\n      return { data: null, error: 'Chat not enabled for this application' };\r\n    }\r\n\r\n    // Check if room already exists\r\n    const { data: existingRoom, error: roomError } = await supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .eq('business_id', businessId)\r\n      .eq('influencer_id', influencerId)\r\n      .eq('campaign_application_id', campaignApplicationId)\r\n      .single();\r\n\r\n    if (existingRoom) {\r\n      return { data: existingRoom, error: null };\r\n    }\r\n\r\n    // Create new room\r\n    const roomTitle = `Kampanja: ${campaignTitle}`;\r\n    const { data: newRoom, error: createError } = await supabase\r\n      .from('chat_rooms')\r\n      .insert({\r\n        business_id: businessId,\r\n        influencer_id: influencerId,\r\n        campaign_application_id: campaignApplicationId,\r\n        room_title: roomTitle,\r\n        room_type: 'campaign_application'\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    return { data: newRoom, error: createError };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get or create a chat room for a direct offer\r\n */\r\nexport async function getOrCreateChatRoomForOffer(\r\n  offerId: string\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    console.log('getOrCreateChatRoomForOffer: Starting for offer:', offerId);\r\n    // First, get the offer details\r\n    const { data: offer, error: offerError } = await supabase\r\n      .from('direct_offers')\r\n      .select('id, title, business_id, influencer_id')\r\n      .eq('id', offerId)\r\n      .single();\r\n\r\n    if (offerError || !offer) {\r\n      console.log('getOrCreateChatRoomForOffer: Offer error:', offerError);\r\n      return { data: null, error: offerError || 'Offer not found' };\r\n    }\r\n\r\n    console.log('getOrCreateChatRoomForOffer: Offer found:', offer);\r\n\r\n    // Check if chat is enabled via permissions\r\n    console.log('getOrCreateChatRoomForOffer: Checking if chat enabled...');\r\n    const chatEnabled = await isChatEnabled(offer.business_id, offer.influencer_id, offerId);\r\n    console.log('getOrCreateChatRoomForOffer: Chat enabled:', chatEnabled);\r\n    if (!chatEnabled) {\r\n      return { data: null, error: 'Chat not enabled for this offer' };\r\n    }\r\n\r\n    // Check if room already exists\r\n    console.log('getOrCreateChatRoomForOffer: Checking for existing room...');\r\n    const { data: existingRoom, error: roomError } = await supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .eq('business_id', offer.business_id)\r\n      .eq('influencer_id', offer.influencer_id)\r\n      .eq('offer_id', offerId)\r\n      .single();\r\n\r\n    console.log('getOrCreateChatRoomForOffer: Existing room check:', { existingRoom, roomError });\r\n\r\n    if (existingRoom) {\r\n      console.log('getOrCreateChatRoomForOffer: Found existing room:', existingRoom);\r\n      return { data: existingRoom, error: null };\r\n    }\r\n\r\n    // Create new room\r\n    const roomTitle = `Direktna ponuda: ${offer.title}`;\r\n    console.log('getOrCreateChatRoomForOffer: Creating new room with title:', roomTitle);\r\n    const { data: newRoom, error: createError } = await supabase\r\n      .from('chat_rooms')\r\n      .insert({\r\n        business_id: offer.business_id,\r\n        influencer_id: offer.influencer_id,\r\n        offer_id: offerId,\r\n        room_title: roomTitle,\r\n        room_type: 'direct_offer'\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    console.log('getOrCreateChatRoomForOffer: Create result:', { newRoom, createError });\r\n    return { data: newRoom, error: createError };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get all chat rooms for current user\r\n */\r\nexport async function getUserChatRooms(): Promise<{ data: ChatRoom[] | null; error: any }> {\r\n\r\n  try {\r\n    console.log('getUserChatRooms: Starting...');\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      console.log('getUserChatRooms: Not authenticated');\r\n      return { data: null, error: 'Not authenticated' };\r\n    }\r\n\r\n    console.log('getUserChatRooms: User ID:', user.user.id);\r\n    const { data: profile } = await supabase\r\n      .from('profiles')\r\n      .select('user_type')\r\n      .eq('id', user.user.id)\r\n      .single();\r\n\r\n    if (!profile) {\r\n      console.log('getUserChatRooms: Profile not found');\r\n      return { data: null, error: 'Profile not found' };\r\n    }\r\n\r\n    console.log('getUserChatRooms: User type:', profile.user_type);\r\n\r\n    // Get rooms based on user type - simplified query first\r\n    console.log('getUserChatRooms: Querying chat_rooms...');\r\n    const query = supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .order('last_message_at', { ascending: false, nullsFirst: false });\r\n\r\n    if (profile.user_type === 'business') {\r\n      query.eq('business_id', user.user.id);\r\n    } else {\r\n      query.eq('influencer_id', user.user.id);\r\n    }\r\n\r\n    const { data: rooms, error } = await query;\r\n    console.log('getUserChatRooms: Query result:', { rooms, error });\r\n\r\n    if (error) {\r\n      console.log('getUserChatRooms: Database error:', error);\r\n      return { data: null, error };\r\n    }\r\n\r\n    console.log('getUserChatRooms: Found rooms:', rooms?.length || 0);\r\n\r\n    // Enhance rooms with profile data\r\n    if (rooms && rooms.length > 0) {\r\n      const enhancedRooms = await Promise.all(\r\n        rooms.map(async (room) => {\r\n          // Get business profile\r\n          const { data: businessProfile } = await supabase\r\n            .from('profiles')\r\n            .select('full_name, username, avatar_url')\r\n            .eq('id', room.business_id)\r\n            .single();\r\n\r\n          // Get influencer profile\r\n          const { data: influencerProfile } = await supabase\r\n            .from('profiles')\r\n            .select('full_name, username, avatar_url')\r\n            .eq('id', room.influencer_id)\r\n            .single();\r\n\r\n          return {\r\n            ...room,\r\n            business_profile: businessProfile,\r\n            influencer_profile: influencerProfile\r\n          };\r\n        })\r\n      );\r\n\r\n      console.log('getUserChatRooms: Enhanced rooms with profiles');\r\n      return { data: enhancedRooms, error: null };\r\n    }\r\n\r\n    return { data: rooms || [], error: null };\r\n  } catch (error) {\r\n    console.log('getUserChatRooms: Catch error:', error);\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get messages for a chat room\r\n */\r\nexport async function getChatMessages(\r\n  roomId: string,\r\n  limit: number = 50,\r\n  offset: number = 0\r\n): Promise<{ data: ChatMessage[] | null; error: any }> {\r\n\r\n  try {\r\n    const { data: messages, error } = await supabase\r\n      .from('chat_messages')\r\n      .select(`\r\n        *,\r\n        sender_profile:profiles!chat_messages_sender_id_fkey(\r\n          full_name,\r\n          username,\r\n          avatar_url\r\n        )\r\n      `)\r\n      .eq('room_id', roomId)\r\n      .order('created_at', { ascending: false })\r\n      .range(offset, offset + limit - 1);\r\n\r\n    if (error) {\r\n      return { data: null, error };\r\n    }\r\n\r\n    // Reverse to show oldest first\r\n    return { data: messages?.reverse() || [], error: null };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Send a message to a chat room\r\n */\r\nexport async function sendChatMessage(\r\n  roomId: string,\r\n  messageText?: string,\r\n  fileUrl?: string,\r\n  fileName?: string,\r\n  fileType?: string,\r\n  fileSize?: number\r\n): Promise<{ data: ChatMessage | null; error: any }> {\r\n\r\n  try {\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      return { data: null, error: 'Not authenticated' };\r\n    }\r\n\r\n    const { data: profile } = await supabase\r\n      .from('profiles')\r\n      .select('user_type')\r\n      .eq('id', user.user.id)\r\n      .single();\r\n\r\n    if (!profile) {\r\n      return { data: null, error: 'Profile not found' };\r\n    }\r\n\r\n    // Validate that either message text or file is provided\r\n    if (!messageText && !fileUrl) {\r\n      return { data: null, error: 'Message must contain text or file' };\r\n    }\r\n\r\n    const { data: message, error } = await supabase\r\n      .from('chat_messages')\r\n      .insert({\r\n        room_id: roomId,\r\n        sender_id: user.user.id,\r\n        sender_type: profile.user_type,\r\n        message_text: messageText,\r\n        file_url: fileUrl,\r\n        file_name: fileName,\r\n        file_type: fileType,\r\n        file_size: fileSize\r\n      })\r\n      .select(`\r\n        *,\r\n        sender_profile:profiles!chat_messages_sender_id_fkey(\r\n          full_name,\r\n          username,\r\n          avatar_url\r\n        )\r\n      `)\r\n      .single();\r\n\r\n    return { data: message, error };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Mark messages as read\r\n */\r\nexport async function markMessagesAsRead(\r\n  roomId: string,\r\n  messageIds?: string[]\r\n): Promise<{ error: any }> {\r\n\r\n  try {\r\n    console.log('markMessagesAsRead called with:', { roomId, messageIds });\r\n\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      console.log('markMessagesAsRead: Not authenticated');\r\n      return { error: 'Not authenticated' };\r\n    }\r\n\r\n    let query = supabase\r\n      .from('chat_messages')\r\n      .update({ read_at: new Date().toISOString() })\r\n      .eq('room_id', roomId)\r\n      .neq('sender_id', user.user.id)\r\n      .is('read_at', null);\r\n\r\n    if (messageIds) {\r\n      console.log('markMessagesAsRead: Adding messageIds filter:', messageIds);\r\n\r\n      // Ensure messageIds is an array\r\n      const idsArray = Array.isArray(messageIds) ? messageIds : [messageIds];\r\n      console.log('markMessagesAsRead: IDs as array:', idsArray);\r\n\r\n      // Validate that all messageIds are valid UUIDs\r\n      const validIds = idsArray.filter(id =>\r\n        typeof id === 'string' &&\r\n        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)\r\n      );\r\n      console.log('markMessagesAsRead: Valid UUIDs:', validIds);\r\n\r\n      if (validIds.length > 0) {\r\n        query = query.in('id', validIds);\r\n      }\r\n    }\r\n\r\n    console.log('markMessagesAsRead: Executing query...');\r\n    const { error } = await query;\r\n    console.log('markMessagesAsRead: Query result:', { error });\r\n    return { error };\r\n  } catch (error) {\r\n    console.log('markMessagesAsRead: Catch error:', error);\r\n    return { error };\r\n  }\r\n}\r\n\r\n/**\r\n * Update participant's last read timestamp\r\n */\r\nexport async function updateLastReadAt(roomId: string): Promise<{ error: any }> {\r\n\r\n  try {\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      return { error: 'Not authenticated' };\r\n    }\r\n\r\n    const { error } = await supabase\r\n      .from('chat_participants')\r\n      .update({ last_read_at: new Date().toISOString() })\r\n      .eq('room_id', roomId)\r\n      .eq('user_id', user.user.id);\r\n\r\n    return { error };\r\n  } catch (error) {\r\n    return { error };\r\n  }\r\n}\r\n\r\n/**\r\n * Enable chat for a campaign application (creates permission and room)\r\n */\r\nexport async function enableChatForApplication(\r\n  campaignApplicationId: string,\r\n  businessApproved: boolean = true,\r\n  influencerApproved: boolean = true\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    // Get application details\r\n    const { data: application, error: appError } = await supabase\r\n      .from('campaign_applications')\r\n      .select(`\r\n        id,\r\n        campaign_id,\r\n        influencer_id,\r\n        campaigns!inner(\r\n          id,\r\n          title,\r\n          business_id\r\n        )\r\n      `)\r\n      .eq('id', campaignApplicationId)\r\n      .single();\r\n\r\n    if (appError || !application) {\r\n      return { data: null, error: appError || 'Application not found' };\r\n    }\r\n\r\n    const businessId = application.campaigns.business_id;\r\n    const influencerId = application.influencer_id;\r\n\r\n    // Create or update chat permission\r\n    await upsertApplicationChatPermission(\r\n      businessId,\r\n      influencerId,\r\n      campaignApplicationId,\r\n      businessApproved,\r\n      influencerApproved\r\n    );\r\n\r\n    // Create chat room\r\n    return await getOrCreateChatRoomForApplication(campaignApplicationId);\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Enable chat for a direct offer (creates permission and room)\r\n */\r\nexport async function enableChatForOffer(\r\n  offerId: string,\r\n  businessApproved: boolean = true,\r\n  influencerApproved: boolean = true\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    console.log('enableChatForOffer: Starting for offer:', offerId);\r\n    // Get offer details\r\n    const { data: offer, error: offerError } = await supabase\r\n      .from('direct_offers')\r\n      .select('id, title, business_id, influencer_id')\r\n      .eq('id', offerId)\r\n      .single();\r\n\r\n    if (offerError || !offer) {\r\n      console.log('enableChatForOffer: Offer error:', offerError);\r\n      return { data: null, error: offerError || 'Offer not found' };\r\n    }\r\n\r\n    console.log('enableChatForOffer: Offer found:', offer);\r\n\r\n    // Create or update chat permission\r\n    console.log('enableChatForOffer: Creating permission...');\r\n    await upsertOfferChatPermission(\r\n      offer.business_id,\r\n      offer.influencer_id,\r\n      offerId,\r\n      businessApproved,\r\n      influencerApproved\r\n    );\r\n\r\n    // Create chat room\r\n    console.log('enableChatForOffer: Creating chat room...');\r\n    const result = await getOrCreateChatRoomForOffer(offerId);\r\n    console.log('enableChatForOffer: Result:', result);\r\n    return result;\r\n  } catch (error) {\r\n    console.log('enableChatForOffer: Catch error:', error);\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get a specific chat room by ID\r\n */\r\nexport async function getChatRoom(roomId: string): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    console.log('getChatRoom: Loading room:', roomId);\r\n\r\n    // First get the basic room data\r\n    const { data: room, error: roomError } = await supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .eq('id', roomId)\r\n      .single();\r\n\r\n    console.log('getChatRoom: Room data:', { room, roomError });\r\n\r\n    if (roomError || !room) {\r\n      return { data: null, error: roomError };\r\n    }\r\n\r\n    // Then get the profile data separately\r\n    const { data: businessProfile } = await supabase\r\n      .from('profiles')\r\n      .select('full_name, username, avatar_url')\r\n      .eq('id', room.business_id)\r\n      .single();\r\n\r\n    const { data: influencerProfile } = await supabase\r\n      .from('profiles')\r\n      .select('full_name, username, avatar_url')\r\n      .eq('id', room.influencer_id)\r\n      .single();\r\n\r\n    console.log('getChatRoom: Profiles:', { businessProfile, influencerProfile });\r\n\r\n    // Combine the data\r\n    const enrichedRoom = {\r\n      ...room,\r\n      business_profile: businessProfile,\r\n      influencer_profile: influencerProfile\r\n    };\r\n\r\n    console.log('getChatRoom: Final room:', enrichedRoom);\r\n    return { data: enrichedRoom, error: null };\r\n  } catch (error) {\r\n    console.log('getChatRoom: Catch error:', error);\r\n    return { data: null, error };\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AA+DO,eAAe,kCACpB,qBAA6B;IAG7B,IAAI;QACF,8CAA8C;QAC9C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,yBACL,MAAM,CAAE,oKAUR,EAAE,CAAC,MAAM,uBACT,MAAM;QAET,IAAI,YAAY,CAAC,aAAa;YAC5B,OAAO;gBAAE,MAAM;gBAAM,OAAO,YAAY;YAAwB;QAClE;QAEA,MAAM,aAAa,YAAY,SAAS,CAAC,WAAW;QACpD,MAAM,eAAe,YAAY,aAAa;QAC9C,MAAM,gBAAgB,YAAY,SAAS,CAAC,KAAK;QAEjD,2CAA2C;QAC3C,MAAM,cAAc,MAAM,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,cAAc,WAAW;QAC7E,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAwC;QACtE;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,2BAA2B,uBAC9B,MAAM;QAET,IAAI,cAAc;YAChB,OAAO;gBAAE,MAAM;gBAAc,OAAO;YAAK;QAC3C;QAEA,kBAAkB;QAClB,MAAM,YAAY,AAAC,aAA0B,OAAd;QAC/B,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACzD,IAAI,CAAC,cACL,MAAM,CAAC;YACN,aAAa;YACb,eAAe;YACf,yBAAyB;YACzB,YAAY;YACZ,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,OAAO;YAAE,MAAM;YAAS,OAAO;QAAY;IAC7C,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,4BACpB,OAAe;IAGf,IAAI;QACF,QAAQ,GAAG,CAAC,oDAAoD;QAChE,+BAA+B;QAC/B,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtD,IAAI,CAAC,iBACL,MAAM,CAAC,yCACP,EAAE,CAAC,MAAM,SACT,MAAM;QAET,IAAI,cAAc,CAAC,OAAO;YACxB,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;gBAAE,MAAM;gBAAM,OAAO,cAAc;YAAkB;QAC9D;QAEA,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,EAAE,MAAM,aAAa,EAAE;QAChF,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAkC;QAChE;QAEA,+BAA+B;QAC/B,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAAM,WAAW,EACnC,EAAE,CAAC,iBAAiB,MAAM,aAAa,EACvC,EAAE,CAAC,YAAY,SACf,MAAM;QAET,QAAQ,GAAG,CAAC,qDAAqD;YAAE;YAAc;QAAU;QAE3F,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC,qDAAqD;YACjE,OAAO;gBAAE,MAAM;gBAAc,OAAO;YAAK;QAC3C;QAEA,kBAAkB;QAClB,MAAM,YAAY,AAAC,oBAA+B,OAAZ,MAAM,KAAK;QACjD,QAAQ,GAAG,CAAC,8DAA8D;QAC1E,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACzD,IAAI,CAAC,cACL,MAAM,CAAC;YACN,aAAa,MAAM,WAAW;YAC9B,eAAe,MAAM,aAAa;YAClC,UAAU;YACV,YAAY;YACZ,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,QAAQ,GAAG,CAAC,+CAA+C;YAAE;YAAS;QAAY;QAClF,OAAO;YAAE,MAAM;YAAS,OAAO;QAAY;IAC7C,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe;IAEpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,QAAQ,GAAG,CAAC,8BAA8B,KAAK,IAAI,CAAC,EAAE;QACtD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EACrB,MAAM;QAET,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,QAAQ,GAAG,CAAC,gCAAgC,QAAQ,SAAS;QAE7D,wDAAwD;QACxD,QAAQ,GAAG,CAAC;QACZ,MAAM,QAAQ,yHAAA,CAAA,WAAQ,CACnB,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC,mBAAmB;YAAE,WAAW;YAAO,YAAY;QAAM;QAElE,IAAI,QAAQ,SAAS,KAAK,YAAY;YACpC,MAAM,EAAE,CAAC,eAAe,KAAK,IAAI,CAAC,EAAE;QACtC,OAAO;YACL,MAAM,EAAE,CAAC,iBAAiB,KAAK,IAAI,CAAC,EAAE;QACxC;QAEA,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QACrC,QAAQ,GAAG,CAAC,mCAAmC;YAAE;YAAO;QAAM;QAE9D,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,QAAQ,GAAG,CAAC,kCAAkC,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI;QAE/D,kCAAkC;QAClC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,MAAM,GAAG,CAAC,OAAO;gBACf,uBAAuB;gBACvB,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,WAAW,EACzB,MAAM;gBAET,yBAAyB;gBACzB,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,aAAa,EAC3B,MAAM;gBAET,OAAO;oBACL,GAAG,IAAI;oBACP,kBAAkB;oBAClB,oBAAoB;gBACtB;YACF;YAGF,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAK;QAC5C;QAEA,OAAO;YAAE,MAAM,SAAS,EAAE;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,gBACpB,MAAc;QACd,QAAA,iEAAgB,IAChB,SAAA,iEAAiB;IAGjB,IAAI;QACF,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,iBACL,MAAM,CAAE,mKAQR,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,IAAI,OAAO;YACT,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,+BAA+B;QAC/B,OAAO;YAAE,MAAM,CAAA,qBAAA,+BAAA,SAAU,OAAO,OAAM,EAAE;YAAE,OAAO;QAAK;IACxD,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,gBACpB,MAAc,EACd,WAAoB,EACpB,OAAgB,EAChB,QAAiB,EACjB,QAAiB,EACjB,QAAiB;IAGjB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EACrB,MAAM;QAET,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,wDAAwD;QACxD,IAAI,CAAC,eAAe,CAAC,SAAS;YAC5B,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoC;QAClE;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,SAAS;YACT,WAAW,KAAK,IAAI,CAAC,EAAE;YACvB,aAAa,QAAQ,SAAS;YAC9B,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,WAAW;QACb,GACC,MAAM,CAAE,mKAQR,MAAM;QAET,OAAO;YAAE,MAAM;YAAS;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,mBACpB,MAAc,EACd,UAAqB;IAGrB,IAAI;QACF,QAAQ,GAAG,CAAC,mCAAmC;YAAE;YAAQ;QAAW;QAEpE,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,OAAO;YAAoB;QACtC;QAEA,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,IAAI,OAAO,WAAW;QAAG,GAC3C,EAAE,CAAC,WAAW,QACd,GAAG,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,EAC7B,EAAE,CAAC,WAAW;QAEjB,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,iDAAiD;YAE7D,gCAAgC;YAChC,MAAM,WAAW,MAAM,OAAO,CAAC,cAAc,aAAa;gBAAC;aAAW;YACtE,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,+CAA+C;YAC/C,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,KAC/B,OAAO,OAAO,YACd,kEAAkE,IAAI,CAAC;YAEzE,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,QAAQ,MAAM,EAAE,CAAC,MAAM;YACzB;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;QACxB,QAAQ,GAAG,CAAC,qCAAqC;YAAE;QAAM;QACzD,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO;YAAE;QAAM;IACjB;AACF;AAKO,eAAe,iBAAiB,MAAc;IAEnD,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO;gBAAE,OAAO;YAAoB;QACtC;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,qBACL,MAAM,CAAC;YAAE,cAAc,IAAI,OAAO,WAAW;QAAG,GAChD,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE;QAE7B,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE;QAAM;IACjB;AACF;AAKO,eAAe,yBACpB,qBAA6B;QAC7B,mBAAA,iEAA4B,MAC5B,qBAAA,iEAA8B;IAG9B,IAAI;QACF,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,yBACL,MAAM,CAAE,oKAUR,EAAE,CAAC,MAAM,uBACT,MAAM;QAET,IAAI,YAAY,CAAC,aAAa;YAC5B,OAAO;gBAAE,MAAM;gBAAM,OAAO,YAAY;YAAwB;QAClE;QAEA,MAAM,aAAa,YAAY,SAAS,CAAC,WAAW;QACpD,MAAM,eAAe,YAAY,aAAa;QAE9C,mCAAmC;QACnC,MAAM,CAAA,GAAA,oIAAA,CAAA,kCAA+B,AAAD,EAClC,YACA,cACA,uBACA,kBACA;QAGF,mBAAmB;QACnB,OAAO,MAAM,kCAAkC;IACjD,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,mBACpB,OAAe;QACf,mBAAA,iEAA4B,MAC5B,qBAAA,iEAA8B;IAG9B,IAAI;QACF,QAAQ,GAAG,CAAC,2CAA2C;QACvD,oBAAoB;QACpB,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtD,IAAI,CAAC,iBACL,MAAM,CAAC,yCACP,EAAE,CAAC,MAAM,SACT,MAAM;QAET,IAAI,cAAc,CAAC,OAAO;YACxB,QAAQ,GAAG,CAAC,oCAAoC;YAChD,OAAO;gBAAE,MAAM;gBAAM,OAAO,cAAc;YAAkB;QAC9D;QAEA,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,mCAAmC;QACnC,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAC5B,MAAM,WAAW,EACjB,MAAM,aAAa,EACnB,SACA,kBACA;QAGF,mBAAmB;QACnB,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,4BAA4B;QACjD,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,YAAY,MAAc;IAE9C,IAAI;QACF,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,gCAAgC;QAChC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACpD,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,QAAQ,GAAG,CAAC,2BAA2B;YAAE;YAAM;QAAU;QAEzD,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAU;QACxC;QAEA,uCAAuC;QACvC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,WAAW,EACzB,MAAM;QAET,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,aAAa,EAC3B,MAAM;QAET,QAAQ,GAAG,CAAC,0BAA0B;YAAE;YAAiB;QAAkB;QAE3E,mBAAmB;QACnB,MAAM,eAAe;YACnB,GAAG,IAAI;YACP,kBAAkB;YAClB,oBAAoB;QACtB;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO;YAAE,MAAM;YAAc,OAAO;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 3154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/ChatList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { MessageCircle, Users, Clock, CheckCircle2, Sparkles } from 'lucide-react';\nimport { ChatRoom as ChatRoomType } from '@/lib/chat';\nimport { getUserChatRooms } from '@/lib/chat';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface ChatListProps {\n  onSelectRoom: (room: ChatRoomType) => void;\n}\n\nexport function ChatList({ onSelectRoom }: ChatListProps) {\n  const { user } = useAuth();\n  const [rooms, setRooms] = useState<ChatRoomType[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (user) {\n      loadChatRooms();\n    }\n  }, [user]);\n\n  const loadChatRooms = async () => {\n    setLoading(true);\n    try {\n      const { data, error } = await getUserChatRooms();\n      if (error) {\n        console.error('Error loading chat rooms:', error);\n      } else {\n        setRooms(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading chat rooms:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getInitials = (name: string | null | undefined) => {\n    if (!name) return '?';\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const formatLastMessageTime = (timestamp: string | null) => {\n    if (!timestamp) return '';\n    \n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    \n    if (diffInHours < 24) {\n      return date.toLocaleTimeString('sr-RS', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('sr-RS', {\n        day: 'numeric',\n        month: 'short'\n      });\n    }\n  };\n\n  const getOtherParticipant = (room: ChatRoomType) => {\n    if (!user) return null;\n    const userType = user.user_metadata?.user_type || 'influencer';\n    \n    if (userType === 'business') {\n      return room.influencer_profile;\n    } else {\n      return room.business_profile;\n    }\n  };\n\n  const getRoomTypeLabel = (roomType: string) => {\n    switch (roomType) {\n      case 'campaign_application':\n        return 'Kampanja';\n      case 'direct_offer':\n        return 'Direktna ponuda';\n      default:\n        return 'Chat';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card className=\"h-full\">\n        <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <MessageCircle className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            Poruke\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"p-4\">\n          <div className=\"space-y-4\">\n            {[1, 2, 3, 4].map((i) => (\n              <div key={i} className=\"flex items-center gap-3 p-3 rounded-lg border\">\n                <Skeleton className=\"h-12 w-12 rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <Skeleton className=\"h-4 w-32\" />\n                    <Skeleton className=\"h-3 w-12\" />\n                  </div>\n                  <Skeleton className=\"h-3 w-24\" />\n                  <Skeleton className=\"h-3 w-full\" />\n                </div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (rooms.length === 0) {\n    return (\n      <Card className=\"h-full\">\n        <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <MessageCircle className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            Poruke\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"flex-1 flex items-center justify-center\">\n          <div className=\"text-center text-muted-foreground py-12\">\n            <div className=\"relative mb-6\">\n              <div className=\"w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto\">\n                <Users className=\"h-10 w-10 text-blue-500\" />\n              </div>\n              <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center\">\n                <Sparkles className=\"h-3 w-3 text-yellow-600\" />\n              </div>\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">Nemate aktivne razgovore</h3>\n            <p className=\"text-sm mb-4\">Razgovori će se pojaviti kada prihvatite ponude ili aplikacije.</p>\n            <div className=\"flex items-center justify-center gap-4 text-xs\">\n              <div className=\"flex items-center gap-1 bg-blue-50 px-3 py-1 rounded-full\">\n                <CheckCircle2 className=\"h-3 w-3 text-blue-600\" />\n                <span className=\"text-blue-700\">Sigurno</span>\n              </div>\n              <div className=\"flex items-center gap-1 bg-green-50 px-3 py-1 rounded-full\">\n                <Clock className=\"h-3 w-3 text-green-600\" />\n                <span className=\"text-green-700\">Trenutno</span>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"h-full\">\n      <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <MessageCircle className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            Poruke\n          </div>\n          <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n            {rooms.length}\n          </Badge>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"p-0 flex-1 overflow-y-auto\">\n        <div className=\"space-y-1 p-2\">\n          {rooms.map((room) => {\n            const otherParticipant = getOtherParticipant(room);\n            const hasUnread = room.unread_count && room.unread_count > 0;\n\n            return (\n              <Button\n                key={room.id}\n                variant=\"ghost\"\n                className={`w-full justify-start p-4 h-auto transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md rounded-lg ${\n                  hasUnread ? 'bg-blue-50/50 border border-blue-200' : ''\n                }`}\n                onClick={() => onSelectRoom(room)}\n              >\n                <div className=\"flex items-center gap-3 w-full\">\n                  <div className=\"relative\">\n                    <Avatar className=\"h-12 w-12 ring-2 ring-white shadow-sm\">\n                      <AvatarImage src={otherParticipant?.avatar_url || ''} />\n                      <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-500 text-white\">\n                        {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}\n                      </AvatarFallback>\n                    </Avatar>\n                    {hasUnread && (\n                      <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center\">\n                        <span className=\"text-xs font-bold text-white\">{room.unread_count}</span>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex-1 text-left\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className={`font-medium ${hasUnread ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>\n                        {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}\n                      </h4>\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-xs text-muted-foreground\">\n                          {formatLastMessageTime(room.last_message_at)}\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between mt-1\">\n                      <p className=\"text-sm text-muted-foreground\">\n                        @{otherParticipant?.username || 'nepoznato'}\n                      </p>\n                      <Badge\n                        variant=\"outline\"\n                        className={`text-xs ${\n                          room.room_type === 'campaign_application'\n                            ? 'border-green-200 bg-green-50 text-green-700'\n                            : 'border-blue-200 bg-blue-50 text-blue-700'\n                        }`}\n                      >\n                        {getRoomTypeLabel(room.room_type)}\n                      </Badge>\n                    </div>\n\n                    <p className={`text-sm mt-1 truncate ${hasUnread ? 'font-medium text-gray-800' : 'text-muted-foreground'}`}>\n                      {room.room_title}\n                    </p>\n                  </div>\n                </div>\n              </Button>\n            );\n          })}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;;;AAXA;;;;;;;;;;AAiBO,SAAS,SAAS,KAA+B;QAA/B,EAAE,YAAY,EAAiB,GAA/B;;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;6BAAG;QAAC;KAAK;IAET,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,OAAO;gBACL,SAAS,QAAQ,EAAE;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,QAAQ;YACV;QACF,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,KAAK;gBACL,OAAO;YACT;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;YAEV;QADjB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,WAAW,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,SAAS,KAAI;QAElD,IAAI,aAAa,YAAY;YAC3B,OAAO,KAAK,kBAAkB;QAChC,OAAO;YACL,OAAO,KAAK,gBAAgB;QAC9B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;4BACrB;;;;;;;;;;;;8BAIV,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAEtB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BARd;;;;;;;;;;;;;;;;;;;;;IAgBtB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;4BACrB;;;;;;;;;;;;8BAIV,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAe;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,wNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;gCACrB;;;;;;;sCAGR,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAClC,MAAM,MAAM;;;;;;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC;wBACV,MAAM,mBAAmB,oBAAoB;wBAC7C,MAAM,YAAY,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG;wBAE3D,qBACE,6LAAC,qIAAA,CAAA,SAAM;4BAEL,SAAQ;4BACR,WAAW,AAAC,uJAEX,OADC,YAAY,yCAAyC;4BAEvD,SAAS,IAAM,aAAa;sCAE5B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,CAAA,6BAAA,uCAAA,iBAAkB,UAAU,KAAI;;;;;;kEAClD,6LAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,mBAAmB,YAAY,iBAAiB,SAAS,IAAI,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;4CAG9F,2BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAgC,KAAK,YAAY;;;;;;;;;;;;;;;;;kDAKvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAW,AAAC,eAA0E,OAA5D,YAAY,gCAAgC;kEACvE,CAAA,6BAAA,uCAAA,iBAAkB,SAAS,MAAI,6BAAA,uCAAA,iBAAkB,QAAQ,KAAI;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,sBAAsB,KAAK,eAAe;;;;;;;;;;;;;;;;;0DAKjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAgC;4DACzC,CAAA,6BAAA,uCAAA,iBAAkB,QAAQ,KAAI;;;;;;;kEAElC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAW,AAAC,WAIX,OAHC,KAAK,SAAS,KAAK,yBACf,gDACA;kEAGL,iBAAiB,KAAK,SAAS;;;;;;;;;;;;0DAIpC,6LAAC;gDAAE,WAAW,AAAC,yBAA0F,OAAlE,YAAY,8BAA8B;0DAC9E,KAAK,UAAU;;;;;;;;;;;;;;;;;;2BAnDjB,KAAK,EAAE;;;;;oBAyDlB;;;;;;;;;;;;;;;;;AAKV;GA3OgB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 3783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/ChatContextBar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  ExternalLink, \n  Calendar, \n  DollarSign, \n  Target,\n  Building2,\n  User\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { supabase } from '@/lib/supabase';\nimport Link from 'next/link';\n\ninterface ChatContextBarProps {\n  campaignApplicationId?: string | null;\n  offerId?: string | null;\n}\n\ninterface CampaignData {\n  id: string;\n  title: string;\n  budget: number;\n  deadline: string | null;\n  status: string;\n  company_name: string;\n}\n\ninterface OfferData {\n  id: string;\n  title: string;\n  budget: number;\n  deadline: string | null;\n  status: string;\n  influencer_name: string;\n  business_name: string;\n}\n\nexport function ChatContextBar({ campaignApplicationId, offerId }: ChatContextBarProps) {\n  const { user } = useAuth();\n  const [campaignData, setCampaignData] = useState<CampaignData | null>(null);\n  const [offerData, setOfferData] = useState<OfferData | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadContextData();\n  }, [campaignApplicationId, offerId]);\n\n  const loadContextData = async () => {\n    setLoading(true);\n    try {\n      if (campaignApplicationId) {\n        await loadCampaignData();\n      } else if (offerId) {\n        await loadOfferData();\n      }\n    } catch (error) {\n      console.error('Error loading context data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCampaignData = async () => {\n    if (!campaignApplicationId) return;\n\n    try {\n      const { data: application, error } = await supabase\n        .from('campaign_applications')\n        .select(`\n          id,\n          status,\n          campaigns (\n            id,\n            title,\n            budget,\n            deadline,\n            status,\n            businesses (\n              company_name\n            )\n          )\n        `)\n        .eq('id', campaignApplicationId)\n        .single();\n\n      if (error) {\n        console.error('Error loading campaign data:', error);\n        return;\n      }\n\n      if (application?.campaigns) {\n        setCampaignData({\n          id: application.campaigns.id,\n          title: application.campaigns.title,\n          budget: application.campaigns.budget,\n          deadline: application.campaigns.deadline,\n          status: application.status,\n          company_name: application.campaigns.businesses?.company_name || 'Nepoznato'\n        });\n      }\n    } catch (error) {\n      console.error('Error loading campaign data:', error);\n    }\n  };\n\n  const loadOfferData = async () => {\n    if (!offerId) return;\n\n    try {\n      // Get offer data with business info\n      const { data: offer, error } = await supabase\n        .from('direct_offers')\n        .select(`\n          id,\n          title,\n          budget,\n          deadline,\n          status,\n          influencer_id,\n          businesses (\n            company_name\n          )\n        `)\n        .eq('id', offerId)\n        .single();\n\n      if (error) {\n        console.error('Error loading offer data:', error);\n        return;\n      }\n\n      // Get influencer profile separately\n      const { data: influencerProfile } = await supabase\n        .from('profiles')\n        .select('username, full_name')\n        .eq('id', offer.influencer_id)\n        .single();\n\n      if (offer) {\n        setOfferData({\n          id: offer.id,\n          title: offer.title,\n          budget: offer.budget,\n          deadline: offer.deadline,\n          status: offer.status || 'pending',\n          business_name: offer.businesses?.company_name || 'Nepoznato',\n          influencer_name: influencerProfile?.full_name || influencerProfile?.username || 'Nepoznato'\n        });\n      }\n    } catch (error) {\n      console.error('Error loading offer data:', error);\n    }\n  };\n\n  const getContextLink = () => {\n    if (!user) return '#';\n    \n    const userType = user.user_metadata?.user_type || 'influencer';\n    \n    if (campaignApplicationId) {\n      if (userType === 'business') {\n        return `/dashboard/biznis/applications/${campaignApplicationId}`;\n      } else {\n        return `/dashboard/influencer/applications/${campaignApplicationId}`;\n      }\n    } else if (offerId) {\n      if (userType === 'business') {\n        return `/dashboard/biznis/offers/${offerId}`;\n      } else {\n        return `/dashboard/influencer/offers/${offerId}`;\n      }\n    }\n    \n    return '#';\n  };\n\n  const getContextTitle = () => {\n    if (campaignData) return campaignData.title;\n    if (offerData) return offerData.title;\n    if (campaignApplicationId) return 'Kampanja';\n    if (offerId) return 'Direktna ponuda';\n    return 'Razgovor';\n  };\n\n  const getContextType = () => {\n    if (campaignApplicationId) return 'Kampanja';\n    if (offerId) return 'Direktna ponuda';\n    return 'Razgovor';\n  };\n\n  const getBudget = () => {\n    if (campaignData) return campaignData.budget;\n    if (offerData) return offerData.budget;\n    return null;\n  };\n\n  const getDeadline = () => {\n    if (campaignData) return campaignData.deadline;\n    if (offerData) return offerData.deadline;\n    return null;\n  };\n\n  const getStatus = () => {\n    if (campaignData) return campaignData.status;\n    if (offerData) return offerData.status;\n    return null;\n  };\n\n  const getStatusBadge = (status: string | null) => {\n    if (!status) return null;\n    \n    switch (status) {\n      case 'active':\n      case 'accepted':\n        return <Badge variant=\"default\" className=\"bg-green-500\">{status === 'active' ? 'Aktivna' : 'Prihvaćeno'}</Badge>;\n      case 'pending':\n        return <Badge variant=\"secondary\">Na čekanju</Badge>;\n      case 'rejected':\n        return <Badge variant=\"destructive\">Odbačeno</Badge>;\n      case 'completed':\n        return <Badge variant=\"outline\" className=\"border-green-500 text-green-700\">Završeno</Badge>;\n      default:\n        return <Badge variant=\"outline\">{status}</Badge>;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card className=\"mb-4\">\n        <CardContent className=\"p-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-900\"></div>\n              <span className=\"text-sm text-muted-foreground\">Učitava kontekst...</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"mb-4 border-l-4 border-l-primary\">\n      <CardContent className=\"p-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center gap-2\">\n              {campaignApplicationId ? (\n                <Target className=\"h-4 w-4 text-primary\" />\n              ) : (\n                <Building2 className=\"h-4 w-4 text-primary\" />\n              )}\n              <div>\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"font-medium text-sm\">{getContextTitle()}</span>\n                  {getStatusBadge(getStatus())}\n                </div>\n                <div className=\"flex items-center gap-4 text-xs text-muted-foreground mt-1\">\n                  <span className=\"flex items-center gap-1\">\n                    <span className=\"font-medium\">{getContextType()}</span>\n                  </span>\n                  {getBudget() && (\n                    <span className=\"flex items-center gap-1\">\n                      <DollarSign className=\"h-3 w-3\" />\n                      {getBudget()?.toLocaleString()} KM\n                    </span>\n                  )}\n                  {getDeadline() && (\n                    <span className=\"flex items-center gap-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      {new Date(getDeadline()!).toLocaleDateString()}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <Button variant=\"outline\" size=\"sm\" asChild>\n            <Link href={getContextLink()}>\n              <ExternalLink className=\"h-3 w-3 mr-1\" />\n              Detalji\n            </Link>\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;AAhBA;;;;;;;;;AA0CO,SAAS,eAAe,KAAuD;QAAvD,EAAE,qBAAqB,EAAE,OAAO,EAAuB,GAAvD;QAmOR;;IAlOrB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;QAAuB;KAAQ;IAEnC,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,IAAI,uBAAuB;gBACzB,MAAM;YACR,OAAO,IAAI,SAAS;gBAClB,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,uBAAuB;QAE5B,IAAI;YACF,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAChD,IAAI,CAAC,yBACL,MAAM,CAAE,+PAcR,EAAE,CAAC,MAAM,uBACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C;YACF;YAEA,IAAI,wBAAA,kCAAA,YAAa,SAAS,EAAE;oBAOV;gBANhB,gBAAgB;oBACd,IAAI,YAAY,SAAS,CAAC,EAAE;oBAC5B,OAAO,YAAY,SAAS,CAAC,KAAK;oBAClC,QAAQ,YAAY,SAAS,CAAC,MAAM;oBACpC,UAAU,YAAY,SAAS,CAAC,QAAQ;oBACxC,QAAQ,YAAY,MAAM;oBAC1B,cAAc,EAAA,oCAAA,YAAY,SAAS,CAAC,UAAU,cAAhC,wDAAA,kCAAkC,YAAY,KAAI;gBAClE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,oCAAoC;YACpC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,iBACL,MAAM,CAAE,mMAWR,EAAE,CAAC,MAAM,SACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C;YACF;YAEA,oCAAoC;YACpC,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,YACL,MAAM,CAAC,uBACP,EAAE,CAAC,MAAM,MAAM,aAAa,EAC5B,MAAM;YAET,IAAI,OAAO;oBAOQ;gBANjB,aAAa;oBACX,IAAI,MAAM,EAAE;oBACZ,OAAO,MAAM,KAAK;oBAClB,QAAQ,MAAM,MAAM;oBACpB,UAAU,MAAM,QAAQ;oBACxB,QAAQ,MAAM,MAAM,IAAI;oBACxB,eAAe,EAAA,oBAAA,MAAM,UAAU,cAAhB,wCAAA,kBAAkB,YAAY,KAAI;oBACjD,iBAAiB,CAAA,8BAAA,wCAAA,kBAAmB,SAAS,MAAI,8BAAA,wCAAA,kBAAmB,QAAQ,KAAI;gBAClF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,iBAAiB;YAGJ;QAFjB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,WAAW,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,SAAS,KAAI;QAElD,IAAI,uBAAuB;YACzB,IAAI,aAAa,YAAY;gBAC3B,OAAO,AAAC,kCAAuD,OAAtB;YAC3C,OAAO;gBACL,OAAO,AAAC,sCAA2D,OAAtB;YAC/C;QACF,OAAO,IAAI,SAAS;YAClB,IAAI,aAAa,YAAY;gBAC3B,OAAO,AAAC,4BAAmC,OAAR;YACrC,OAAO;gBACL,OAAO,AAAC,gCAAuC,OAAR;YACzC;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,cAAc,OAAO,aAAa,KAAK;QAC3C,IAAI,WAAW,OAAO,UAAU,KAAK;QACrC,IAAI,uBAAuB,OAAO;QAClC,IAAI,SAAS,OAAO;QACpB,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,uBAAuB,OAAO;QAClC,IAAI,SAAS,OAAO;QACpB,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,IAAI,cAAc,OAAO,aAAa,MAAM;QAC5C,IAAI,WAAW,OAAO,UAAU,MAAM;QACtC,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI,cAAc,OAAO,aAAa,QAAQ;QAC9C,IAAI,WAAW,OAAO,UAAU,QAAQ;QACxC,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,IAAI,cAAc,OAAO,aAAa,MAAM;QAC5C,IAAI,WAAW,OAAO,UAAU,MAAM;QACtC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAgB,WAAW,WAAW,YAAY;;;;;;YAC9F,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAkC;;;;;;YAC9E;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5D;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,sCACC,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;gDACtC,eAAe;;;;;;;sDAElB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACd,cAAA,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;gDAEhC,6BACC,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;yDACrB,aAAA,yBAAA,iCAAA,WAAa,cAAc;wDAAG;;;;;;;gDAGlC,+BACC,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,IAAI,KAAK,eAAgB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQxD,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,OAAO;kCACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;;8CACV,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GA3PgB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 4250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/ChatRoom.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Send, ArrowLeft, Clock, CheckCircle2, User, MessageCircle } from 'lucide-react';\nimport { ChatMessage, ChatRoom as ChatRoomType } from '@/lib/chat';\nimport { getChatMessages, sendChatMessage, markMessagesAsRead } from '@/lib/chat';\nimport { supabase } from '@/lib/supabase';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { ChatContextBar } from './ChatContextBar';\n\ninterface ChatRoomProps {\n  room: ChatRoomType;\n  onBack: () => void;\n}\n\nexport function ChatRoom({ room, onBack }: ChatRoomProps) {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    loadMessages();\n    \n    // Subscribe to new messages\n    const channel = supabase\n      .channel(`chat_room_${room.id}`)\n      .on(\n        'postgres_changes',\n        {\n          event: 'INSERT',\n          schema: 'public',\n          table: 'chat_messages',\n          filter: `room_id=eq.${room.id}`,\n        },\n        (payload) => {\n          const newMessage = payload.new as ChatMessage;\n          setMessages(prev => [...prev, newMessage]);\n        }\n      )\n      .subscribe();\n\n    return () => {\n      supabase.removeChannel(channel);\n    };\n  }, [room.id]);\n\n  const loadMessages = async () => {\n    setLoading(true);\n    try {\n      const { data, error } = await getChatMessages(room.id);\n      if (error) {\n        console.error('Error loading messages:', error);\n      } else {\n        setMessages(data || []);\n        // Mark unread messages as read (messages not sent by current user)\n        if (user && data) {\n          const unreadMessageIds = data\n            .filter(msg => msg.sender_id !== user.id && !msg.read_at)\n            .map(msg => msg.id);\n\n          if (unreadMessageIds.length > 0) {\n            await markMessagesAsRead(room.id, unreadMessageIds);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newMessage.trim() || !user || sending) return;\n\n    setSending(true);\n    try {\n      const userType = user.user_metadata?.user_type || 'influencer';\n      const { data, error } = await sendChatMessage(\n        room.id,\n        newMessage.trim()\n      );\n\n      if (error) {\n        console.error('Error sending message:', error);\n      } else {\n        setNewMessage('');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const getInitials = (name: string | null | undefined) => {\n    if (!name) return '?';\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const formatTime = (timestamp: string) => {\n    return new Date(timestamp).toLocaleTimeString('sr-RS', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatDate = (timestamp: string) => {\n    return new Date(timestamp).toLocaleDateString('sr-RS', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n\n  const isMyMessage = (message: ChatMessage) => {\n    return user && message.sender_id === user.id;\n  };\n\n  const getOtherParticipant = () => {\n    if (!user) return null;\n    const userType = user.user_metadata?.user_type || 'influencer';\n    \n    if (userType === 'business') {\n      return room.influencer_profile;\n    } else {\n      return room.business_profile;\n    }\n  };\n\n  const otherParticipant = getOtherParticipant();\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Context Bar */}\n      <ChatContextBar\n        campaignApplicationId={room.campaign_application_id}\n        offerId={room.offer_id}\n      />\n\n      <Card className=\"flex-1 flex flex-col shadow-lg\">\n        {/* Enhanced Header */}\n        <CardHeader className=\"flex-shrink-0 border-b bg-gradient-to-r from-blue-50 to-purple-50\">\n          <div className=\"flex items-center gap-3\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onBack}\n              className=\"p-2 hover:bg-white/80 rounded-full\"\n            >\n              <ArrowLeft className=\"h-4 w-4\" />\n            </Button>\n\n            <div className=\"relative\">\n              <Avatar className=\"h-12 w-12 ring-2 ring-white shadow-md\">\n                <AvatarImage src={otherParticipant?.avatar_url || ''} />\n                <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-500 text-white\">\n                  {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}\n                </AvatarFallback>\n              </Avatar>\n              <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\"></div>\n            </div>\n\n            <div className=\"flex-1\">\n              <CardTitle className=\"text-lg text-gray-900\">\n                {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}\n              </CardTitle>\n              <div className=\"flex items-center gap-2\">\n                <p className=\"text-sm text-muted-foreground\">\n                  @{otherParticipant?.username || 'nepoznato'}\n                </p>\n                <div className=\"flex items-center gap-1 text-xs text-green-600\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span>Online</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Badge\n                variant=\"secondary\"\n                className={`${\n                  room.room_type === 'campaign_application'\n                    ? 'bg-green-100 text-green-800 border-green-200'\n                    : 'bg-blue-100 text-blue-800 border-blue-200'\n                }`}\n              >\n                <MessageCircle className=\"h-3 w-3 mr-1\" />\n                {room.room_type === 'campaign_application' ? 'Kampanja' : 'Direktna ponuda'}\n              </Badge>\n            </div>\n          </div>\n        </CardHeader>\n\n      <CardContent className=\"flex-1 flex flex-col p-0\">\n        {/* Enhanced Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-gray-50/30 to-white\">\n          {loading ? (\n            <div className=\"space-y-4\">\n              {[1, 2, 3, 4].map((i) => (\n                <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>\n                  <div className=\"max-w-[70%] space-y-2\">\n                    <Skeleton className=\"h-12 w-48 rounded-lg\" />\n                    <Skeleton className=\"h-3 w-16\" />\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : messages.length === 0 ? (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"text-center text-muted-foreground py-12\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <MessageCircle className=\"h-8 w-8 text-blue-500\" />\n                </div>\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Počnite razgovor</h3>\n                <p className=\"text-sm\">Pošaljite prvu poruku da započnete komunikaciju!</p>\n              </div>\n            </div>\n          ) : (\n            messages.map((message, index) => {\n              const showDate = index === 0 ||\n                formatDate(message.created_at || '') !== formatDate(messages[index - 1]?.created_at || '');\n\n              return (\n                <div key={message.id}>\n                  {showDate && (\n                    <div className=\"text-center text-xs text-muted-foreground mb-4\">\n                      <div className=\"bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full inline-block border\">\n                        {formatDate(message.created_at || '')}\n                      </div>\n                    </div>\n                  )}\n\n                  <div className={`flex ${isMyMessage(message) ? 'justify-end' : 'justify-start'}`}>\n                    <div className={`max-w-[70%] ${isMyMessage(message) ? 'order-2' : 'order-1'}`}>\n                      <div\n                        className={`rounded-2xl px-4 py-3 shadow-sm ${\n                          isMyMessage(message)\n                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'\n                            : 'bg-white border border-gray-200'\n                        }`}\n                      >\n                        <p className=\"text-sm leading-relaxed\">{message.message_text}</p>\n                      </div>\n                      <div className={`flex items-center gap-1 mt-1 ${\n                        isMyMessage(message) ? 'justify-end' : 'justify-start'\n                      }`}>\n                        <Clock className=\"h-3 w-3 text-muted-foreground\" />\n                        <p className=\"text-xs text-muted-foreground\">\n                          {formatTime(message.created_at || '')}\n                        </p>\n                        {isMyMessage(message) && (\n                          <CheckCircle2 className=\"h-3 w-3 text-blue-500\" />\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              );\n            })\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Enhanced Message Input */}\n        <div className=\"border-t bg-white p-4\">\n          <form onSubmit={handleSendMessage} className=\"flex gap-3\">\n            <Input\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              placeholder=\"Napišite poruku...\"\n              disabled={sending}\n              className=\"flex-1 rounded-full border-gray-300 focus:border-blue-500 focus:ring-blue-500\"\n            />\n            <Button\n              type=\"submit\"\n              disabled={!newMessage.trim() || sending}\n              className=\"rounded-full w-12 h-12 p-0 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg\"\n            >\n              {sending ? (\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n              ) : (\n                <Send className=\"h-4 w-4\" />\n              )}\n            </Button>\n          </form>\n        </div>\n      </CardContent>\n    </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AAqBO,SAAS,SAAS,KAA+B;QAA/B,EAAE,IAAI,EAAE,MAAM,EAAiB,GAA/B;;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;YAEA,4BAA4B;YAC5B,MAAM,UAAU,yHAAA,CAAA,WAAQ,CACrB,OAAO,CAAC,AAAC,aAAoB,OAAR,KAAK,EAAE,GAC5B,EAAE,CACD,oBACA;gBACE,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,QAAQ,AAAC,cAAqB,OAAR,KAAK,EAAE;YAC/B;8CACA,CAAC;oBACC,MAAM,aAAa,QAAQ,GAAG;oBAC9B;sDAAY,CAAA,OAAQ;mCAAI;gCAAM;6BAAW;;gBAC3C;6CAED,SAAS;YAEZ;sCAAO;oBACL,yHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;gBACzB;;QACF;6BAAG;QAAC,KAAK,EAAE;KAAC;IAEZ,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YACrD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,OAAO;gBACL,YAAY,QAAQ,EAAE;gBACtB,mEAAmE;gBACnE,IAAI,QAAQ,MAAM;oBAChB,MAAM,mBAAmB,KACtB,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,OAAO,EACvD,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;oBAEpB,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBAC/B,MAAM,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,EAAE,EAAE;oBACpC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,QAAQ,SAAS;QAE5C,WAAW;QACX,IAAI;gBACe;YAAjB,MAAM,WAAW,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,SAAS,KAAI;YAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAC1C,KAAK,EAAE,EACP,WAAW,IAAI;YAGjB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,SAAS;YACrD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,SAAS;YACrD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,QAAQ,QAAQ,SAAS,KAAK,KAAK,EAAE;IAC9C;IAEA,MAAM,sBAAsB;YAET;QADjB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,WAAW,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,SAAS,KAAI;QAElD,IAAI,aAAa,YAAY;YAC3B,OAAO,KAAK,kBAAkB;QAChC,OAAO;YACL,OAAO,KAAK,gBAAgB;QAC9B;IACF;IAEA,MAAM,mBAAmB;IAEzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,+IAAA,CAAA,iBAAc;gBACb,uBAAuB,KAAK,uBAAuB;gBACnD,SAAS,KAAK,QAAQ;;;;;;0BAGxB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCAEd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,CAAA,6BAAA,uCAAA,iBAAkB,UAAU,KAAI;;;;;;8DAClD,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,mBAAmB,YAAY,iBAAiB,SAAS,IAAI,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;sDAG/F,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,CAAA,6BAAA,uCAAA,iBAAkB,SAAS,MAAI,6BAAA,uCAAA,iBAAkB,QAAQ,KAAI;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAAgC;wDACzC,CAAA,6BAAA,uCAAA,iBAAkB,QAAQ,KAAI;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAKZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,WAAW,AAAC,GAIX,OAHC,KAAK,SAAS,KAAK,yBACf,iDACA;;0DAGN,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CACxB,KAAK,SAAS,KAAK,yBAAyB,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;oCACZ,wBACC,6LAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;gDAAY,WAAW,AAAC,QAAqD,OAA9C,IAAI,MAAM,IAAI,gBAAgB;0DAC5D,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;+CAHd;;;;;;;;;+CAQZ,SAAS,MAAM,KAAK,kBACtB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;+CAI3B,SAAS,GAAG,CAAC,CAAC,SAAS;4CAEiC;wCADtD,MAAM,WAAW,UAAU,KACzB,WAAW,QAAQ,UAAU,IAAI,QAAQ,WAAW,EAAA,aAAA,QAAQ,CAAC,QAAQ,EAAE,cAAnB,iCAAA,WAAqB,UAAU,KAAI;wCAEzF,qBACE,6LAAC;;gDACE,0BACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ,UAAU,IAAI;;;;;;;;;;;8DAKxC,6LAAC;oDAAI,WAAW,AAAC,QAA8D,OAAvD,YAAY,WAAW,gBAAgB;8DAC7D,cAAA,6LAAC;wDAAI,WAAW,AAAC,eAA2D,OAA7C,YAAY,WAAW,YAAY;;0EAChE,6LAAC;gEACC,WAAW,AAAC,mCAIX,OAHC,YAAY,WACR,4DACA;0EAGN,cAAA,6LAAC;oEAAE,WAAU;8EAA2B,QAAQ,YAAY;;;;;;;;;;;0EAE9D,6LAAC;gEAAI,WAAW,AAAC,gCAEhB,OADC,YAAY,WAAW,gBAAgB;;kFAEvC,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;wEAAE,WAAU;kFACV,WAAW,QAAQ,UAAU,IAAI;;;;;;oEAEnC,YAAY,0BACX,6LAAC,wNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2CA5BxB,QAAQ,EAAE;;;;;oCAmCxB;kDAEF,6LAAC;wCAAI,KAAK;;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;oCAAmB,WAAU;;sDAC3C,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,CAAC,WAAW,IAAI,MAAM;4CAChC,WAAU;sDAET,wBACC,6LAAC;gDAAI,WAAU;;;;;qEAEf,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShC;GArSgB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 4860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/Chat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChatList } from './ChatList';\nimport { ChatRoom } from './ChatRoom';\nimport { ChatRoom as ChatRoomType, getChatRoom } from '@/lib/chat';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\n\ninterface ChatProps {\n  initialRoomId?: string;\n}\n\nexport function Chat({ initialRoomId }: ChatProps) {\n  const [selectedRoom, setSelectedRoom] = useState<ChatRoomType | null>(null);\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    if (initialRoomId) {\n      loadRoom(initialRoomId);\n    }\n  }, [initialRoomId]);\n\n  const loadRoom = async (roomId: string) => {\n    setLoading(true);\n    try {\n      const { data: room, error } = await getChatRoom(roomId);\n      if (room && !error) {\n        setSelectedRoom(room);\n      }\n    } catch (error) {\n      console.error('Error loading room:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSelectRoom = (room: ChatRoomType) => {\n    setSelectedRoom(room);\n  };\n\n  const handleBackToList = () => {\n    setSelectedRoom(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"h-[600px] w-full\">\n        <Card className=\"h-full\">\n          <CardHeader className=\"space-y-2\">\n            <Skeleton className=\"h-6 w-32\" />\n            <Skeleton className=\"h-4 w-48\" />\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {/* Chat room skeleton */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-3\">\n                <Skeleton className=\"h-10 w-10 rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <Skeleton className=\"h-4 w-32\" />\n                  <Skeleton className=\"h-3 w-24\" />\n                </div>\n                <Skeleton className=\"h-6 w-16\" />\n              </div>\n              <div className=\"space-y-3\">\n                {[1, 2, 3, 4].map((i) => (\n                  <div key={i} className=\"flex items-start gap-3\">\n                    <Skeleton className=\"h-8 w-8 rounded-full\" />\n                    <div className=\"flex-1 space-y-2\">\n                      <Skeleton className=\"h-4 w-full\" />\n                      <Skeleton className=\"h-4 w-3/4\" />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-[600px] w-full\">\n      <div className=\"h-full rounded-lg overflow-hidden shadow-lg border\">\n        {selectedRoom ? (\n          <ChatRoom room={selectedRoom} onBack={handleBackToList} />\n        ) : (\n          <ChatList onSelectRoom={handleSelectRoom} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAaO,SAAS,KAAK,KAA4B;QAA5B,EAAE,aAAa,EAAa,GAA5B;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,eAAe;gBACjB,SAAS;YACX;QACF;yBAAG;QAAC;KAAc;IAElB,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;YAChD,IAAI,QAAQ,CAAC,OAAO;gBAClB,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCAErB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;4CAAY,WAAU;;8DACrB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC,uIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;2CAJd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAc1B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,6BACC,6LAAC,yIAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAc,QAAQ;;;;;qCAEtC,6LAAC,yIAAA,CAAA,WAAQ;gBAAC,cAAc;;;;;;;;;;;;;;;;AAKlC;GAhFgB;KAAA", "debugId": null}}, {"offset": {"line": 5108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/dashboard/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useSearchParams } from 'next/navigation';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\nimport { Chat } from '@/components/chat/Chat';\nimport { MessageCircle, Users, Sparkles } from 'lucide-react';\n\nexport default function ChatPage() {\n  const searchParams = useSearchParams();\n  const roomId = searchParams.get('room');\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Enhanced Header */}\n        <div className=\"relative overflow-hidden rounded-lg bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white\">\n          <div className=\"relative z-10\">\n            <div className=\"flex items-center gap-3 mb-2\">\n              <div className=\"p-2 bg-white/20 rounded-lg\">\n                <MessageCircle className=\"h-6 w-6\" />\n              </div>\n              <h1 className=\"text-3xl font-bold tracking-tight\">Poruke</h1>\n            </div>\n            <p className=\"text-green-100 mb-4\">\n              Komunicirajte sa partnerima o kampanjama i ponudama\n            </p>\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-2 text-sm bg-white/20 px-3 py-1 rounded-full\">\n                <Users className=\"h-4 w-4\" />\n                <span>Sigurna komunikacija</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm bg-white/20 px-3 py-1 rounded-full\">\n                <Sparkles className=\"h-4 w-4\" />\n                <span>Trenutne poruke</span>\n              </div>\n            </div>\n          </div>\n          <div className=\"absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10\" />\n          <div className=\"absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5\" />\n        </div>\n\n        <Chat initialRoomId={roomId || undefined} />\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,qBACE,6LAAC,qJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAGnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,6LAAC,qIAAA,CAAA,OAAI;oBAAC,eAAe,UAAU;;;;;;;;;;;;;;;;;AAIvC;GArCwB;;QACD,qIAAA,CAAA,kBAAe;;;KADd", "debugId": null}}]}