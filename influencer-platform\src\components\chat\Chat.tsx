'use client';

import { useState, useEffect } from 'react';
import { ChatList } from './ChatList';
import { ChatRoom } from './ChatRoom';
import { ChatRoom as ChatRoomType, getChatRoom } from '@/lib/chat';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface ChatProps {
  initialRoomId?: string;
}

export function Chat({ initialRoomId }: ChatProps) {
  const [selectedRoom, setSelectedRoom] = useState<ChatRoomType | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (initialRoomId) {
      loadRoom(initialRoomId);
    }
  }, [initialRoomId]);

  const loadRoom = async (roomId: string) => {
    setLoading(true);
    try {
      const { data: room, error } = await getChatRoom(roomId);
      if (room && !error) {
        setSelectedRoom(room);
      }
    } catch (error) {
      console.error('Error loading room:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectRoom = (room: ChatRoomType) => {
    setSelectedRoom(room);
  };

  const handleBackToList = () => {
    setSelectedRoom(null);
  };

  if (loading) {
    return (
      <div className="h-[600px] w-full">
        <Card className="h-full">
          <CardHeader className="space-y-2">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Chat room skeleton */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="space-y-3">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="flex items-start gap-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-[600px] w-full">
      <div className="h-full rounded-lg overflow-hidden shadow-lg border">
        {selectedRoom ? (
          <ChatRoom room={selectedRoom} onBack={handleBackToList} />
        ) : (
          <ChatList onSelectRoom={handleSelectRoom} />
        )}
      </div>
    </div>
  );
}
